"""
PDF导出路由

提供PDF报告生成和下载功能
"""

from fastapi import APIRouter, HTTPException, Path, Query, Response
from fastapi.responses import FileResponse
from typing import Optional
import logging
import os

from ..services.pdf_service import pdf_service
from ..services.analysis_service import analysis_service

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/pdf", tags=["PDF导出"])

@router.post("/generate/{session_id}")
async def generate_pdf_report(
    session_id: str = Path(..., description="会话ID"),
    template_type: str = Query("standard", description="模板类型: standard, detailed, summary"),
    include_charts: bool = Query(True, description="是否包含图表")
):
    """生成PDF分析报告"""
    try:
        # 获取分析结果
        db = await analysis_service._get_db()
        
        # 首先尝试获取高级分析结果
        advanced_result = await db.advanced_analysis_results.find_one(
            {"session_id": session_id},
            sort=[("analysis_timestamp", -1)]
        )
        
        if advanced_result:
            # 移除MongoDB的_id字段
            advanced_result.pop("_id", None)
            
            # 使用高级PDF生成
            pdf_result = await pdf_service.generate_advanced_analysis_pdf(
                session_id=session_id,
                analysis_result=advanced_result,
                template_type="advanced"
            )
        else:
            # 尝试获取基础分析结果
            basic_result = await db.conversation_analyses.find_one(
                {"session_id": session_id},
                sort=[("analysis_timestamp", -1)]
            )
            
            if not basic_result:
                raise HTTPException(status_code=404, detail="未找到分析结果")
            
            basic_result.pop("_id", None)
            
            # 使用基础PDF生成
            pdf_result = await pdf_service.generate_analysis_pdf(
                session_id=session_id,
                analysis_result=basic_result,
                template_type=template_type,
                include_charts=include_charts
            )
        
        return {
            "success": True,
            "message": "PDF生成成功",
            "pdf_info": pdf_result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"PDF生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"PDF生成失败: {str(e)}")

@router.get("/download/{filename}")
async def download_pdf(filename: str = Path(..., description="PDF文件名")):
    """下载PDF文件"""
    try:
        # 构建文件路径
        pdf_path = os.path.join(pdf_service.pdf_storage_path, filename)
        
        # 检查文件是否存在
        if not os.path.exists(pdf_path):
            raise HTTPException(status_code=404, detail="PDF文件不存在")
        
        # 返回文件
        return FileResponse(
            path=pdf_path,
            filename=filename,
            media_type='application/pdf',
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"PDF下载失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"PDF下载失败: {str(e)}")

@router.get("/preview/{filename}")
async def preview_pdf(filename: str = Path(..., description="PDF文件名")):
    """预览PDF文件"""
    try:
        # 构建文件路径
        pdf_path = os.path.join(pdf_service.pdf_storage_path, filename)
        
        # 检查文件是否存在
        if not os.path.exists(pdf_path):
            raise HTTPException(status_code=404, detail="PDF文件不存在")
        
        # 返回文件用于预览
        return FileResponse(
            path=pdf_path,
            filename=filename,
            media_type='application/pdf',
            headers={
                "Content-Disposition": f"inline; filename={filename}"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"PDF预览失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"PDF预览失败: {str(e)}")

@router.get("/list")
async def list_pdf_files(
    limit: int = Query(10, description="返回结果数量限制"),
    offset: int = Query(0, description="偏移量")
):
    """获取PDF文件列表"""
    try:
        db = await analysis_service._get_db()
        
        # 获取PDF记录
        pdf_records = await db.pdf_exports.find(
            {},
            {"session_id": 1, "filename": 1, "file_size": 1, "created_at": 1, "expires_at": 1}
        ).sort("created_at", -1).skip(offset).limit(limit).to_list(length=limit)
        
        # 移除_id字段
        for record in pdf_records:
            record.pop("_id", None)
        
        return {
            "success": True,
            "pdf_files": pdf_records,
            "total": len(pdf_records)
        }
        
    except Exception as e:
        logger.error(f"获取PDF列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取PDF列表失败: {str(e)}")

@router.delete("/delete/{filename}")
async def delete_pdf(filename: str = Path(..., description="PDF文件名")):
    """删除PDF文件"""
    try:
        # 构建文件路径
        pdf_path = os.path.join(pdf_service.pdf_storage_path, filename)
        
        # 删除文件
        if os.path.exists(pdf_path):
            os.remove(pdf_path)
        
        # 从数据库删除记录
        db = await analysis_service._get_db()
        delete_result = await db.pdf_exports.delete_one({"filename": filename})
        
        if delete_result.deleted_count == 0:
            logger.warning(f"数据库中未找到PDF记录: {filename}")
        
        return {
            "success": True,
            "message": f"PDF文件 {filename} 删除成功"
        }
        
    except Exception as e:
        logger.error(f"删除PDF失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除PDF失败: {str(e)}")

@router.post("/batch-generate")
async def batch_generate_pdfs(session_ids: list[str]):
    """批量生成PDF报告"""
    try:
        results = []
        failed_sessions = []
        
        for session_id in session_ids:
            try:
                # 获取分析结果
                db = await analysis_service._get_db()
                
                # 尝试获取高级分析结果
                advanced_result = await db.advanced_analysis_results.find_one(
                    {"session_id": session_id},
                    sort=[("analysis_timestamp", -1)]
                )
                
                if advanced_result:
                    advanced_result.pop("_id", None)
                    pdf_result = await pdf_service.generate_advanced_analysis_pdf(
                        session_id=session_id,
                        analysis_result=advanced_result
                    )
                else:
                    # 尝试基础分析结果
                    basic_result = await db.conversation_analyses.find_one(
                        {"session_id": session_id},
                        sort=[("analysis_timestamp", -1)]
                    )
                    
                    if basic_result:
                        basic_result.pop("_id", None)
                        pdf_result = await pdf_service.generate_analysis_pdf(
                            session_id=session_id,
                            analysis_result=basic_result
                        )
                    else:
                        raise Exception("未找到分析结果")
                
                results.append({
                    "session_id": session_id,
                    "success": True,
                    "pdf_filename": pdf_result.get("pdf_filename"),
                    "download_url": pdf_result.get("download_url")
                })
                
            except Exception as e:
                failed_sessions.append({
                    "session_id": session_id,
                    "error": str(e)
                })
        
        return {
            "success": True,
            "message": f"批量生成完成，成功: {len(results)}, 失败: {len(failed_sessions)}",
            "results": results,
            "failed_sessions": failed_sessions
        }
        
    except Exception as e:
        logger.error(f"批量生成PDF失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量生成PDF失败: {str(e)}")

@router.get("/templates")
async def get_pdf_templates():
    """获取可用的PDF模板"""
    templates = [
        {
            "name": "standard",
            "display_name": "标准模板",
            "description": "包含基本分析结果和建议",
            "features": ["基本信息", "分析结果", "改进建议"]
        },
        {
            "name": "detailed",
            "display_name": "详细模板",
            "description": "包含详细的分析过程和数据",
            "features": ["详细分析", "数据图表", "对话记录", "专业建议"]
        },
        {
            "name": "summary",
            "display_name": "摘要模板",
            "description": "简洁的摘要报告",
            "features": ["执行摘要", "关键指标", "核心建议"]
        },
        {
            "name": "advanced",
            "display_name": "高级模板",
            "description": "五维度详细分析报告",
            "features": ["五维度分析", "详细反馈", "改进建议", "对话摘要"]
        }
    ]
    
    return {
        "success": True,
        "templates": templates
    }

@router.get("/statistics")
async def get_pdf_statistics():
    """获取PDF生成统计信息"""
    try:
        db = await analysis_service._get_db()
        
        # 统计PDF数量
        total_pdfs = await db.pdf_exports.count_documents({})
        
        # 统计文件大小
        size_pipeline = [
            {"$group": {"_id": None, "total_size": {"$sum": "$file_size"}}}
        ]
        size_result = await db.pdf_exports.aggregate(size_pipeline).to_list(length=1)
        total_size = size_result[0]["total_size"] if size_result else 0
        
        # 统计最近7天的生成数量
        from datetime import datetime, timedelta
        week_ago = datetime.now() - timedelta(days=7)
        recent_count = await db.pdf_exports.count_documents({
            "created_at": {"$gte": week_ago}
        })
        
        return {
            "success": True,
            "statistics": {
                "total_pdfs": total_pdfs,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "recent_week_count": recent_count,
                "average_size_mb": round((total_size / total_pdfs) / (1024 * 1024), 2) if total_pdfs > 0 else 0
            }
        }
        
    except Exception as e:
        logger.error(f"获取PDF统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取PDF统计失败: {str(e)}")
