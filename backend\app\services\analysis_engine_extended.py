"""
分析引擎扩展方法

包含科室分配、对话质量和总体评价的分析方法
"""

from typing import Dict, List, Any, Optional
import statistics
from ..models.conversation import ConversationMessage
from ..models.virtual_patient import VirtualPatientPrompt
from .analysis_engine import AnalysisDimension

class AnalysisEngineExtended:
    """分析引擎扩展类"""
    
    def __init__(self, base_engine):
        self.base_engine = base_engine
    
    async def analyze_department_accuracy(
        self,
        patient_prompt: VirtualPatientPrompt,
        suggested_department: Optional[str]
    ) -> AnalysisDimension:
        """分析科室分配正确性维度"""
        try:
            correct_department = patient_prompt.correct_department.value if patient_prompt.correct_department else "内科"
            
            if not suggested_department:
                suggested_department = "未明确建议"
            
            # 计算科室分配准确性
            accuracy_score = self._calculate_department_accuracy_score(
                suggested_department, correct_department
            )
            
            # 计算转诊时机合适性
            timing_score = self._calculate_referral_timing_score(suggested_department)
            
            sub_scores = {
                "accuracy_score": accuracy_score,
                "timing_score": timing_score
            }
            
            total_score = statistics.mean(sub_scores.values())
            
            detailed_feedback = self._generate_department_feedback(
                suggested_department, correct_department, accuracy_score
            )
            
            recommendations = self._generate_department_recommendations(sub_scores)
            
            return AnalysisDimension(
                name="分配科室的正确性",
                score=total_score,
                max_score=100.0,
                weight=self.base_engine.dimension_weights["department_accuracy"],
                detailed_feedback=detailed_feedback,
                sub_scores=sub_scores,
                recommendations=recommendations
            )
            
        except Exception as e:
            return self.base_engine._get_default_dimension("分配科室的正确性", "department_accuracy")
    
    def _calculate_department_accuracy_score(
        self, 
        suggested_department: str, 
        correct_department: str
    ) -> float:
        """计算科室分配准确性得分"""
        if suggested_department == "未明确建议":
            return 30.0
        
        suggested_clean = suggested_department.lower().strip()
        correct_clean = correct_department.lower().strip()
        
        if suggested_clean == correct_clean:
            return 100.0
        elif correct_clean in suggested_clean or suggested_clean in correct_clean:
            return 80.0
        else:
            # 检查是否是相关科室
            related_departments = {
                "心内科": ["内科", "急诊科"],
                "消化内科": ["内科", "急诊科"],
                "呼吸内科": ["内科", "急诊科"],
                "神经内科": ["内科", "急诊科"]
            }
            
            for dept, related in related_departments.items():
                if (dept in correct_clean and suggested_clean in related) or \
                   (dept in suggested_clean and correct_clean in related):
                    return 60.0
            
            return 40.0
    
    def _calculate_referral_timing_score(self, suggested_department: str) -> float:
        """计算转诊时机合适性得分"""
        if suggested_department == "未明确建议":
            return 50.0
        
        # 这里可以根据具体情况评估转诊时机
        # 简化处理，给一个基础分数
        return 80.0
    
    def _generate_department_feedback(
        self, 
        suggested_department: str, 
        correct_department: str, 
        accuracy_score: float
    ) -> str:
        """生成科室分配详细反馈"""
        feedback_parts = []
        
        if accuracy_score >= 80:
            feedback_parts.append("科室分配准确。")
        elif accuracy_score >= 60:
            feedback_parts.append("科室分配基本合理。")
        else:
            feedback_parts.append("科室分配需要改进。")
        
        feedback_parts.append(f"正确科室：{correct_department}")
        feedback_parts.append(f"建议科室：{suggested_department}")
        
        return " ".join(feedback_parts)
    
    def _generate_department_recommendations(self, sub_scores: Dict[str, float]) -> List[str]:
        """生成科室分配改进建议"""
        recommendations = []
        
        if sub_scores["accuracy_score"] < 80:
            recommendations.append("加强对各科室诊疗范围的了解")
            recommendations.append("根据患者症状特点选择合适的专科")
        
        if sub_scores["timing_score"] < 70:
            recommendations.append("注意转诊时机的把握，及时转诊疑难病例")
        
        return recommendations
    
    async def analyze_conversation_quality(
        self,
        messages: List[ConversationMessage],
        patient_prompt: VirtualPatientPrompt
    ) -> AnalysisDimension:
        """分析对话质量维度"""
        try:
            doctor_messages = [msg for msg in messages if msg.role == "doctor"]
            
            # 1. 信息收集完整性
            information_completeness = self._calculate_information_completeness(
                doctor_messages, patient_prompt
            )
            
            # 2. 问诊技巧
            questioning_skills = self._calculate_questioning_skills(doctor_messages)
            
            # 3. 沟通效率
            communication_efficiency = self._calculate_communication_efficiency(messages)
            
            # 4. 专业性
            professionalism = self._calculate_professionalism(doctor_messages)
            
            sub_scores = {
                "information_completeness": information_completeness,
                "questioning_skills": questioning_skills,
                "communication_efficiency": communication_efficiency,
                "professionalism": professionalism
            }
            
            total_score = statistics.mean(sub_scores.values())
            
            detailed_feedback = self._generate_quality_feedback(sub_scores)
            recommendations = self._generate_quality_recommendations(sub_scores)
            
            return AnalysisDimension(
                name="对话质量分析",
                score=total_score,
                max_score=100.0,
                weight=self.base_engine.dimension_weights["conversation_quality"],
                detailed_feedback=detailed_feedback,
                sub_scores=sub_scores,
                recommendations=recommendations
            )
            
        except Exception as e:
            return self.base_engine._get_default_dimension("对话质量分析", "conversation_quality")
    
    def _calculate_information_completeness(
        self, 
        doctor_messages: List[ConversationMessage], 
        patient_prompt: VirtualPatientPrompt
    ) -> float:
        """计算信息收集完整性得分"""
        required_info = [
            "主要症状", "症状持续时间", "诱发因素", "伴随症状",
            "既往史", "用药史", "过敏史"
        ]
        
        collected_info = 0
        for info in required_info:
            for message in doctor_messages:
                if any(keyword in message.content for keyword in [info, info.replace("史", "")]):
                    collected_info += 1
                    break
        
        completeness_rate = collected_info / len(required_info)
        return completeness_rate * 100
    
    def _calculate_questioning_skills(self, doctor_messages: List[ConversationMessage]) -> float:
        """计算问诊技巧得分"""
        if not doctor_messages:
            return 0.0
        
        skills_score = 70.0  # 基础分
        
        # 检查开放性问题
        open_questions = ["请描述", "能详细说说", "具体是什么", "还有什么"]
        open_count = 0
        for message in doctor_messages:
            if any(pattern in message.content for pattern in open_questions):
                open_count += 1
        
        skills_score += min(open_count * 5, 20)
        
        # 检查封闭性问题
        closed_questions = ["是否", "有没有", "是不是", "会不会"]
        closed_count = 0
        for message in doctor_messages:
            if any(pattern in message.content for pattern in closed_questions):
                closed_count += 1
        
        skills_score += min(closed_count * 2, 10)
        
        return min(skills_score, 100.0)
    
    def _calculate_communication_efficiency(self, messages: List[ConversationMessage]) -> float:
        """计算沟通效率得分"""
        if not messages:
            return 0.0
        
        total_rounds = len([msg for msg in messages if msg.role == "doctor"])
        
        # 理想轮数为8-12轮
        if 8 <= total_rounds <= 12:
            efficiency_score = 100.0
        elif 6 <= total_rounds < 8 or 12 < total_rounds <= 15:
            efficiency_score = 80.0
        elif total_rounds < 6:
            efficiency_score = 60.0  # 信息收集不足
        else:
            efficiency_score = 70.0  # 效率较低
        
        return efficiency_score
    
    def _calculate_professionalism(self, doctor_messages: List[ConversationMessage]) -> float:
        """计算专业性得分"""
        if not doctor_messages:
            return 0.0
        
        professionalism_score = 80.0  # 基础分
        
        # 检查医学术语使用
        medical_terms = ["症状", "体征", "诊断", "治疗", "检查", "病史"]
        term_count = 0
        for message in doctor_messages:
            for term in medical_terms:
                if term in message.content:
                    term_count += 1
                    break
        
        professionalism_score += min(term_count * 2, 15)
        
        # 检查不当表达
        inappropriate_expressions = ["肯定是", "绝对", "一定是", "不可能"]
        for message in doctor_messages:
            if any(expr in message.content for expr in inappropriate_expressions):
                professionalism_score -= 5
        
        return max(min(professionalism_score, 100.0), 0.0)
    
    def _generate_quality_feedback(self, sub_scores: Dict[str, float]) -> str:
        """生成对话质量详细反馈"""
        feedback_parts = []
        
        avg_score = statistics.mean(sub_scores.values())
        if avg_score >= 85:
            feedback_parts.append("对话质量优秀。")
        elif avg_score >= 70:
            feedback_parts.append("对话质量良好。")
        else:
            feedback_parts.append("对话质量有待提高。")
        
        if sub_scores["information_completeness"] < 70:
            feedback_parts.append("信息收集不够完整。")
        
        if sub_scores["questioning_skills"] < 70:
            feedback_parts.append("问诊技巧需要改进。")
        
        return " ".join(feedback_parts)
    
    def _generate_quality_recommendations(self, sub_scores: Dict[str, float]) -> List[str]:
        """生成对话质量改进建议"""
        recommendations = []
        
        if sub_scores["information_completeness"] < 70:
            recommendations.append("按照病史采集的标准流程进行系统性问诊")
        
        if sub_scores["questioning_skills"] < 70:
            recommendations.append("合理运用开放性和封闭性问题")
        
        if sub_scores["communication_efficiency"] < 70:
            recommendations.append("提高问诊效率，避免重复或无关问题")
        
        if sub_scores["professionalism"] < 80:
            recommendations.append("使用准确的医学术语，保持专业性")
        
        return recommendations
    
    async def generate_overall_assessment(
        self,
        messages: List[ConversationMessage],
        patient_prompt: VirtualPatientPrompt
    ) -> AnalysisDimension:
        """生成总体评价维度"""
        try:
            # 综合评价各个方面
            overall_score = 75.0  # 基础分
            
            # 根据对话轮数调整
            doctor_messages = [msg for msg in messages if msg.role == "doctor"]
            rounds = len(doctor_messages)
            
            if 8 <= rounds <= 12:
                overall_score += 10
            elif rounds < 6:
                overall_score -= 10
            
            # 根据对话完整性调整
            if self._is_conversation_complete(messages):
                overall_score += 10
            else:
                overall_score -= 5
            
            detailed_feedback = "基于整体对话表现的综合评价。"
            recommendations = ["继续保持良好的问诊习惯", "注意与患者的人文关怀"]
            
            return AnalysisDimension(
                name="总体建议和评价",
                score=min(overall_score, 100.0),
                max_score=100.0,
                weight=self.base_engine.dimension_weights["overall_assessment"],
                detailed_feedback=detailed_feedback,
                sub_scores={"overall_performance": overall_score},
                recommendations=recommendations
            )
            
        except Exception as e:
            return self.base_engine._get_default_dimension("总体建议和评价", "overall_assessment")
    
    def _is_conversation_complete(self, messages: List[ConversationMessage]) -> bool:
        """判断对话是否完整"""
        doctor_messages = [msg for msg in messages if msg.role == "doctor"]
        
        # 检查是否有诊断或建议
        final_patterns = ["诊断", "建议", "治疗", "复查", "注意"]
        
        if doctor_messages:
            last_message = doctor_messages[-1].content
            return any(pattern in last_message for pattern in final_patterns)
        
        return False
