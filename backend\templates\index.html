<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚拟患者对话系统</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/vue-router@4/dist/vue-router.global.js"></script>
    <script src="https://unpkg.com/vuex@4/dist/vuex.global.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
        }
        
        .app-container {
            min-height: 100vh;
        }
        
        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.3s;
        }
        
        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }
        
        /* 首页样式 */
        .virtual-patient-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .app-title {
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .start-container {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        .welcome-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .welcome-description {
            color: #666;
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div id="app">
        <router-view></router-view>
    </div>

    <script>
        const { createApp } = Vue;
        const { createRouter, createWebHashHistory } = VueRouter;
        const { createStore } = Vuex;

        // Vuex Store
        const store = createStore({
            state: {
                currentPatient: null,
                sessionId: null,
                analysisResult: null
            },
            mutations: {
                setCurrentPatient(state, patient) {
                    state.currentPatient = patient;
                },
                setSessionId(state, sessionId) {
                    state.sessionId = sessionId;
                    localStorage.setItem('sessionId', sessionId);
                },
                setAnalysisResult(state, result) {
                    state.analysisResult = result;
                    localStorage.setItem('analysisResult', JSON.stringify(result));
                }
            }
        });

        // 组件定义
        const Home = {
            template: `
                <div class="virtual-patient-container">
                    <header class="header">
                        <div class="header-content">
                            <h1 class="app-title">虚拟患者对话系统</h1>
                        </div>
                    </header>
                    <main class="main-content">
                        <div class="start-container">
                            <h2 class="welcome-title">欢迎使用虚拟患者对话系统</h2>
                            <p class="welcome-description">
                                与AI虚拟患者进行医患对话练习，提升您的诊疗技能和沟通能力
                            </p>
                            <button 
                                class="btn btn-primary" 
                                :class="{ loading: isGeneratingPatient }"
                                @click="startSession"
                                :disabled="isGeneratingPatient"
                            >
                                {{ isGeneratingPatient ? '正在生成患者...' : '开始对话' }}
                            </button>
                        </div>
                    </main>
                </div>
            `,
            data() {
                return {
                    isGeneratingPatient: false
                };
            },
            methods: {
                async startSession() {
                    this.isGeneratingPatient = true;
                    try {
                        // 模拟API调用
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        
                        // 模拟生成患者数据
                        const mockPatient = {
                            id: 'patient_' + Date.now(),
                            name: '张三',
                            age: 35,
                            gender: '男',
                            symptoms: '头痛、失眠'
                        };
                        
                        const sessionId = 'session_' + Date.now();
                        
                        this.$store.commit('setCurrentPatient', mockPatient);
                        this.$store.commit('setSessionId', sessionId);
                        
                        this.$router.push('/conversation');
                    } catch (error) {
                        console.error('生成患者失败:', error);
                        alert('生成患者失败，请重试');
                    } finally {
                        this.isGeneratingPatient = false;
                    }
                }
            }
        };

        const Conversation = {
            template: `
                <div style="padding: 20px; text-align: center;">
                    <h1>对话页面</h1>
                    <p>这里是与虚拟患者的对话界面</p>
                    <button class="btn btn-primary" @click="goToResult">查看分析结果</button>
                </div>
            `,
            methods: {
                goToResult() {
                    // 模拟分析结果
                    const mockResult = {
                        overall_score: 85,
                        grade: 'A',
                        analysis_timestamp: new Date().toISOString()
                    };
                    this.$store.commit('setAnalysisResult', mockResult);
                    this.$router.push('/result');
                }
            }
        };

        const Result = {
            template: `
                <div style="padding: 20px; text-align: center;">
                    <h1>分析结果页面</h1>
                    <p>这里显示对话分析结果</p>
                    <div v-if="analysisResult">
                        <p>总分: {{ analysisResult.overall_score }}</p>
                        <p>等级: {{ analysisResult.grade }}</p>
                    </div>
                    <button class="btn btn-primary" @click="newSession">新的对话</button>
                </div>
            `,
            computed: {
                analysisResult() {
                    return this.$store.state.analysisResult;
                }
            },
            methods: {
                newSession() {
                    this.$router.push('/');
                }
            }
        };

        // 路由配置
        const routes = [
            { path: '/', component: Home },
            { path: '/conversation', component: Conversation },
            { path: '/result', component: Result }
        ];

        const router = createRouter({
            history: createWebHashHistory(),
            routes
        });

        // 创建应用
        const app = createApp({});
        app.use(store);
        app.use(router);
        app.use(ElementPlus);
        app.mount('#app');
    </script>
</body>
</html>
