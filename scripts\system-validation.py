#!/usr/bin/env python3
"""
虚拟患者对话系统验证脚本

验证系统的完整性和功能正确性
"""

import asyncio
import aiohttp
import json
import time
import sys
from typing import Dict, Any, List
from datetime import datetime

class SystemValidator:
    """系统验证器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        self.test_results = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def log_test(self, test_name: str, success: bool, message: str = "", duration: float = 0):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "duration": duration,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        duration_str = f" ({duration:.2f}s)" if duration > 0 else ""
        print(f"{status} {test_name}{duration_str}")
        if message:
            print(f"    {message}")
    
    async def test_health_check(self):
        """测试健康检查"""
        start_time = time.time()
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    duration = time.time() - start_time
                    self.log_test("健康检查", True, f"状态: {data.get('status', 'unknown')}", duration)
                    return True
                else:
                    duration = time.time() - start_time
                    self.log_test("健康检查", False, f"HTTP {response.status}", duration)
                    return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("健康检查", False, str(e), duration)
            return False
    
    async def test_monitoring_endpoints(self):
        """测试监控端点"""
        endpoints = [
            "/api/monitoring/health",
            "/api/monitoring/metrics", 
            "/api/monitoring/status/summary"
        ]
        
        for endpoint in endpoints:
            start_time = time.time()
            try:
                async with self.session.get(f"{self.base_url}{endpoint}") as response:
                    duration = time.time() - start_time
                    if response.status == 200:
                        self.log_test(f"监控端点 {endpoint}", True, "", duration)
                    else:
                        self.log_test(f"监控端点 {endpoint}", False, f"HTTP {response.status}", duration)
            except Exception as e:
                duration = time.time() - start_time
                self.log_test(f"监控端点 {endpoint}", False, str(e), duration)
    
    async def test_patient_generation(self):
        """测试患者生成"""
        start_time = time.time()
        try:
            payload = {
                "difficulty_level": 1,
                "department_preference": "cardiology"
            }
            
            async with self.session.post(
                f"{self.base_url}/api/virtual-patient/generate",
                json=payload
            ) as response:
                duration = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    if data.get("success") and "patient" in data and "session_id" in data:
                        self.log_test("患者生成", True, f"会话ID: {data['session_id']}", duration)
                        return data
                    else:
                        self.log_test("患者生成", False, "响应格式错误", duration)
                        return None
                else:
                    self.log_test("患者生成", False, f"HTTP {response.status}", duration)
                    return None
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("患者生成", False, str(e), duration)
            return None
    
    async def test_conversation_flow(self, patient_data: Dict[str, Any]):
        """测试对话流程"""
        if not patient_data:
            self.log_test("对话流程", False, "缺少患者数据")
            return None
        
        session_id = patient_data["session_id"]
        patient_id = patient_data["patient"]["id"]
        
        # 开始对话
        start_time = time.time()
        try:
            payload = {
                "session_id": session_id,
                "patient_id": patient_id
            }
            
            async with self.session.post(
                f"{self.base_url}/api/conversation/start",
                json=payload
            ) as response:
                duration = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    if data.get("success"):
                        self.log_test("对话启动", True, "", duration)
                    else:
                        self.log_test("对话启动", False, "启动失败", duration)
                        return None
                else:
                    self.log_test("对话启动", False, f"HTTP {response.status}", duration)
                    return None
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("对话启动", False, str(e), duration)
            return None
        
        # 发送消息
        start_time = time.time()
        try:
            payload = {
                "session_id": session_id,
                "message": "您好，请问您哪里不舒服？",
                "message_type": "text"
            }
            
            async with self.session.post(
                f"{self.base_url}/api/conversation/message",
                json=payload
            ) as response:
                duration = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    if data.get("success") and "patient_response" in data:
                        self.log_test("消息发送", True, f"患者回复: {data['patient_response'][:50]}...", duration)
                        return session_id
                    else:
                        self.log_test("消息发送", False, "无患者回复", duration)
                        return None
                else:
                    self.log_test("消息发送", False, f"HTTP {response.status}", duration)
                    return None
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("消息发送", False, str(e), duration)
            return None
    
    async def test_analysis_system(self, session_id: str):
        """测试分析系统"""
        if not session_id:
            self.log_test("分析系统", False, "缺少会话ID")
            return None
        
        start_time = time.time()
        try:
            async with self.session.post(
                f"{self.base_url}/api/conversation-analysis/advanced/{session_id}"
            ) as response:
                duration = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    if data.get("success") and "analysis_result" in data:
                        analysis = data["analysis_result"]
                        score = analysis.get("overall_score", 0)
                        self.log_test("分析系统", True, f"总分: {score:.1f}", duration)
                        return analysis
                    else:
                        self.log_test("分析系统", False, "分析结果格式错误", duration)
                        return None
                else:
                    self.log_test("分析系统", False, f"HTTP {response.status}", duration)
                    return None
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("分析系统", False, str(e), duration)
            return None
    
    async def test_pdf_generation(self, session_id: str):
        """测试PDF生成"""
        if not session_id:
            self.log_test("PDF生成", False, "缺少会话ID")
            return
        
        start_time = time.time()
        try:
            async with self.session.post(
                f"{self.base_url}/api/pdf/generate/{session_id}"
            ) as response:
                duration = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    if data.get("success") and "pdf_info" in data:
                        pdf_info = data["pdf_info"]
                        filename = pdf_info.get("pdf_filename", "unknown")
                        self.log_test("PDF生成", True, f"文件: {filename}", duration)
                    else:
                        self.log_test("PDF生成", False, "PDF信息格式错误", duration)
                else:
                    self.log_test("PDF生成", False, f"HTTP {response.status}", duration)
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("PDF生成", False, str(e), duration)
    
    async def test_performance(self):
        """测试性能"""
        start_time = time.time()
        
        # 并发测试
        tasks = []
        for i in range(5):
            task = self.session.get(f"{self.base_url}/health")
            tasks.append(task)
        
        try:
            responses = await asyncio.gather(*tasks)
            duration = time.time() - start_time
            
            success_count = sum(1 for resp in responses if resp.status == 200)
            
            if success_count == 5:
                self.log_test("并发性能", True, f"5个并发请求", duration)
            else:
                self.log_test("并发性能", False, f"只有{success_count}/5个请求成功", duration)
            
            # 清理响应
            for resp in responses:
                resp.close()
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("并发性能", False, str(e), duration)
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始系统验证...")
        print("=" * 50)
        
        # 基础健康检查
        health_ok = await self.test_health_check()
        if not health_ok:
            print("❌ 基础健康检查失败，停止测试")
            return False
        
        # 监控端点测试
        await self.test_monitoring_endpoints()
        
        # 核心功能测试
        patient_data = await self.test_patient_generation()
        session_id = await self.test_conversation_flow(patient_data)
        analysis_result = await self.test_analysis_system(session_id)
        await self.test_pdf_generation(session_id)
        
        # 性能测试
        await self.test_performance()
        
        # 生成报告
        self.generate_report()
        
        return True
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 50)
        print("📊 测试报告")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result['message']}")
        
        # 保存详细报告
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": (passed_tests/total_tests)*100
            },
            "test_results": self.test_results
        }
        
        with open("validation_report.json", "w", encoding="utf-8") as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: validation_report.json")
        
        if failed_tests == 0:
            print("\n🎉 所有测试通过！系统验证成功！")
            return True
        else:
            print(f"\n⚠️  有 {failed_tests} 个测试失败，请检查系统配置")
            return False

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="虚拟患者对话系统验证")
    parser.add_argument("--url", default="http://localhost:8000", help="系统URL")
    args = parser.parse_args()
    
    async with SystemValidator(args.url) as validator:
        success = await validator.run_all_tests()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
