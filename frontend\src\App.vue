<template>
  <div class="app-container">
    <!-- 路由视图 -->
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
  </div>
</template>

<script>
export default {
  name: 'App',
  data() {
    return {
      // 应用全局状态
      appReady: false
    };
  },
  created() {
    // 应用初始化逻辑
    this.initializeApp();
  },
  methods: {
    // 初始化应用
    async initializeApp() {
      try {
        // 检查后端API是否可用
        await this.checkBackendConnection();
        this.appReady = true;
      } catch (error) {
        console.error('应用初始化失败:', error);
      }
    },
    
    // 检查后端连接
    async checkBackendConnection() {
      try {
        // 尝试连接后端健康检查接口
        await this.$axios.get('/health');
        console.log('后端连接成功');
        return true;
      } catch (error) {
        console.warn('后端连接失败，将在5秒后重试...');
        // 5秒后重试
        setTimeout(() => this.checkBackendConnection(), 5000);
        return false;
      }
    }
  }
};
</script>

<style>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  background-color: #f5f7fa;
  color: #333;
  overflow: hidden;
}

.app-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 文本选择样式 */
::selection {
  background: rgba(64, 158, 255, 0.2);
  color: #409eff;
}

/* 添加焦点元素的过渡效果 */
a, button, input, textarea, select {
  transition: all 0.3s ease;
  outline: none;
}

a:focus, button:focus, input:focus, textarea:focus, select:focus {
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}
</style>