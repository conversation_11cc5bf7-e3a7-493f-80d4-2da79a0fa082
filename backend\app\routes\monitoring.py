"""
系统监控和健康检查路由

提供系统状态监控、性能指标和健康检查接口
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import psutil
import asyncio
import logging
from datetime import datetime

from ..core.performance import get_performance_report, performance_monitor, memory_cache, connection_pool
from ..services.database import get_database
from ..services.ai_model_manager import ai_model_manager

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/monitoring", tags=["系统监控"])

@router.get("/health")
async def health_check():
    """系统健康检查"""
    try:
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "components": {}
        }
        
        # 检查数据库连接
        try:
            db = await get_database()
            await db.command("ping")
            health_status["components"]["database"] = {
                "status": "healthy",
                "message": "数据库连接正常"
            }
        except Exception as e:
            health_status["components"]["database"] = {
                "status": "unhealthy",
                "message": f"数据库连接失败: {str(e)}"
            }
            health_status["status"] = "degraded"
        
        # 检查AI模型服务
        try:
            ai_status = ai_model_manager.get_service_status()
            healthy_services = sum(1 for service in ai_status.values() if service["is_healthy"])
            total_services = len(ai_status)
            
            if healthy_services == total_services:
                health_status["components"]["ai_services"] = {
                    "status": "healthy",
                    "message": f"所有AI服务正常 ({healthy_services}/{total_services})"
                }
            elif healthy_services > 0:
                health_status["components"]["ai_services"] = {
                    "status": "degraded",
                    "message": f"部分AI服务异常 ({healthy_services}/{total_services})"
                }
                health_status["status"] = "degraded"
            else:
                health_status["components"]["ai_services"] = {
                    "status": "unhealthy",
                    "message": "所有AI服务异常"
                }
                health_status["status"] = "unhealthy"
        except Exception as e:
            health_status["components"]["ai_services"] = {
                "status": "unknown",
                "message": f"AI服务状态检查失败: {str(e)}"
            }
        
        # 检查系统资源
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            resource_status = "healthy"
            resource_message = "系统资源正常"
            
            if cpu_percent > 80:
                resource_status = "warning"
                resource_message = f"CPU使用率过高: {cpu_percent:.1f}%"
            elif memory.percent > 80:
                resource_status = "warning"
                resource_message = f"内存使用率过高: {memory.percent:.1f}%"
            elif disk.percent > 80:
                resource_status = "warning"
                resource_message = f"磁盘使用率过高: {disk.percent:.1f}%"
            
            health_status["components"]["system_resources"] = {
                "status": resource_status,
                "message": resource_message,
                "details": {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "disk_percent": disk.percent
                }
            }
            
            if resource_status == "warning" and health_status["status"] == "healthy":
                health_status["status"] = "degraded"
                
        except Exception as e:
            health_status["components"]["system_resources"] = {
                "status": "unknown",
                "message": f"系统资源检查失败: {str(e)}"
            }
        
        return health_status
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

@router.get("/metrics")
async def get_metrics():
    """获取系统指标"""
    try:
        # 获取性能报告
        performance_report = get_performance_report()
        
        # 获取系统指标
        system_metrics = {
            "cpu": {
                "percent": psutil.cpu_percent(interval=1),
                "count": psutil.cpu_count(),
                "load_avg": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
            },
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "percent": psutil.virtual_memory().percent,
                "used": psutil.virtual_memory().used
            },
            "disk": {
                "total": psutil.disk_usage('/').total,
                "used": psutil.disk_usage('/').used,
                "free": psutil.disk_usage('/').free,
                "percent": psutil.disk_usage('/').percent
            },
            "network": {
                "bytes_sent": psutil.net_io_counters().bytes_sent,
                "bytes_recv": psutil.net_io_counters().bytes_recv,
                "packets_sent": psutil.net_io_counters().packets_sent,
                "packets_recv": psutil.net_io_counters().packets_recv
            }
        }
        
        # 获取进程信息
        process = psutil.Process()
        process_metrics = {
            "pid": process.pid,
            "cpu_percent": process.cpu_percent(),
            "memory_percent": process.memory_percent(),
            "memory_info": process.memory_info()._asdict(),
            "num_threads": process.num_threads(),
            "create_time": process.create_time()
        }
        
        return {
            "timestamp": datetime.now().isoformat(),
            "performance": performance_report,
            "system": system_metrics,
            "process": process_metrics
        }
        
    except Exception as e:
        logger.error(f"获取系统指标失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统指标失败: {str(e)}")

@router.get("/ai-services")
async def get_ai_services_status():
    """获取AI服务状态"""
    try:
        return {
            "timestamp": datetime.now().isoformat(),
            "services": ai_model_manager.get_service_status()
        }
    except Exception as e:
        logger.error(f"获取AI服务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取AI服务状态失败: {str(e)}")

@router.get("/database")
async def get_database_status():
    """获取数据库状态"""
    try:
        db = await get_database()
        
        # 获取数据库统计信息
        stats = await db.command("dbStats")
        
        # 获取集合统计
        collections = await db.list_collection_names()
        collection_stats = {}
        
        for collection_name in collections:
            try:
                coll_stats = await db.command("collStats", collection_name)
                collection_stats[collection_name] = {
                    "count": coll_stats.get("count", 0),
                    "size": coll_stats.get("size", 0),
                    "avgObjSize": coll_stats.get("avgObjSize", 0)
                }
            except Exception:
                collection_stats[collection_name] = {"error": "无法获取统计信息"}
        
        return {
            "timestamp": datetime.now().isoformat(),
            "connection_status": "connected",
            "database_stats": {
                "db": stats.get("db"),
                "collections": stats.get("collections", 0),
                "objects": stats.get("objects", 0),
                "dataSize": stats.get("dataSize", 0),
                "storageSize": stats.get("storageSize", 0),
                "indexes": stats.get("indexes", 0),
                "indexSize": stats.get("indexSize", 0)
            },
            "collection_stats": collection_stats
        }
        
    except Exception as e:
        logger.error(f"获取数据库状态失败: {str(e)}")
        return {
            "timestamp": datetime.now().isoformat(),
            "connection_status": "error",
            "error": str(e)
        }

@router.get("/cache")
async def get_cache_status():
    """获取缓存状态"""
    try:
        cache_stats = memory_cache.get_stats()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "cache_stats": cache_stats,
            "connection_pool": connection_pool.get_stats()
        }
        
    except Exception as e:
        logger.error(f"获取缓存状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取缓存状态失败: {str(e)}")

@router.post("/cache/clear")
async def clear_cache():
    """清空缓存"""
    try:
        memory_cache.clear()
        return {
            "success": True,
            "message": "缓存已清空",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"清空缓存失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清空缓存失败: {str(e)}")

@router.get("/logs")
async def get_recent_logs(lines: int = 100):
    """获取最近的日志"""
    try:
        # 这里应该读取日志文件
        # 简化实现，返回性能监控中的错误信息
        metrics = performance_monitor.get_metrics()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "error_counts": metrics.get("error_counts", {}),
            "recent_errors": "日志功能需要进一步实现"
        }
        
    except Exception as e:
        logger.error(f"获取日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取日志失败: {str(e)}")

@router.get("/performance/report")
async def get_performance_report_endpoint():
    """获取性能报告"""
    try:
        report = get_performance_report()
        
        # 添加建议
        recommendations = []
        
        # 检查性能指标并给出建议
        system_health = report["performance_metrics"].get("system_health", {})
        if system_health.get("score", 100) < 80:
            recommendations.append("系统响应时间较慢，建议检查服务器资源")
        
        cache_stats = report.get("cache_stats", {})
        if cache_stats.get("hit_rate", 1) < 0.8:
            recommendations.append("缓存命中率较低，建议优化缓存策略")
        
        connection_stats = report.get("connection_pool_stats", {})
        if connection_stats.get("active_connections", 0) > connection_stats.get("max_connections", 10) * 0.8:
            recommendations.append("连接池使用率较高，建议增加连接池大小")
        
        report["recommendations"] = recommendations
        
        return report
        
    except Exception as e:
        logger.error(f"获取性能报告失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取性能报告失败: {str(e)}")

@router.get("/status/summary")
async def get_status_summary():
    """获取状态摘要"""
    try:
        # 获取各组件状态
        health = await health_check()
        ai_services = await get_ai_services_status()
        db_status = await get_database_status()
        
        # 计算总体状态
        overall_status = "healthy"
        if health["status"] != "healthy":
            overall_status = health["status"]
        
        # 统计AI服务
        ai_services_count = len(ai_services["services"])
        healthy_ai_services = sum(1 for service in ai_services["services"].values() if service["is_healthy"])
        
        return {
            "timestamp": datetime.now().isoformat(),
            "overall_status": overall_status,
            "components": {
                "database": db_status["connection_status"] == "connected",
                "ai_services": f"{healthy_ai_services}/{ai_services_count}",
                "cache": True,  # 假设缓存总是可用的
                "system_resources": health["components"]["system_resources"]["status"] == "healthy"
            },
            "uptime": psutil.boot_time(),
            "version": "1.0.0"
        }
        
    except Exception as e:
        logger.error(f"获取状态摘要失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取状态摘要失败: {str(e)}")
