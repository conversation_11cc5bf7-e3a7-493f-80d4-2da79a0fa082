# AI模型部署指南

本文档详细说明如何部署和配置虚拟患者对话系统中的AI模型，包括数字人生成、对话AI、分析AI和诊断评估AI。

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端 Vue.js 应用                         │
│  ┌─────────────────┐  ┌─────────────────────────────────┐   │
│  │   对话界面      │  │        分析结果展示              │   │
│  │   数字人展示    │  │        PDF导出                  │   │
│  └─────────────────┘  └─────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                    后端 FastAPI 服务                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  AI服务接口层                           │ │
│  │  虚拟患者生成 | 对话管理 | 分析评估 | 诊断评估          │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    AI模型服务层                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ 数字人生成模型   │  │   对话AI模型    │  │ 分析AI模型  │ │
│  │   端口: 8001    │  │   端口: 8002    │  │ 端口: 8003  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
│  ┌─────────────────┐                                       │
│  │ 诊断评估AI模型   │                                       │
│  │   端口: 8004    │                                       │
│  └─────────────────┘                                       │
└─────────────────────────────────────────────────────────────┘
```

## 1. 数字人生成模型部署

### 1.1 推荐模型

#### 选项1: Stable Diffusion + ControlNet (推荐)
- **优势**: 开源免费，可控性强，质量高
- **部署方式**: 本地GPU部署
- **资源需求**: 8GB+ GPU显存

#### 选项2: DALL-E 3 API
- **优势**: 质量极高，易于集成
- **部署方式**: API调用
- **费用**: 按图片生成数量计费

#### 选项3: Midjourney API
- **优势**: 艺术质量高
- **部署方式**: API调用
- **费用**: 订阅制

### 1.2 Stable Diffusion本地部署

**avatar_generation_service.py**:
```python
from fastapi import FastAPI
from pydantic import BaseModel
import torch
from diffusers import StableDiffusionPipeline, ControlNetModel, StableDiffusionControlNetPipeline
from PIL import Image
import base64
import io
import uuid
import os

app = FastAPI()

# 加载模型
device = "cuda" if torch.cuda.is_available() else "cpu"
pipe = StableDiffusionPipeline.from_pretrained(
    "runwayml/stable-diffusion-v1-5",
    torch_dtype=torch.float16 if device == "cuda" else torch.float32
).to(device)

class AvatarRequest(BaseModel):
    patient_info: dict
    style: str = "realistic"
    quality: str = "high"

@app.post("/generate-avatar")
async def generate_avatar(request: AvatarRequest):
    try:
        # 构建prompt
        patient = request.patient_info
        age_desc = get_age_description(patient.get("age", 30))
        gender_desc = "male" if patient.get("gender") == "male" else "female"
        
        # 基础prompt
        base_prompt = f"professional medical portrait of a {age_desc} {gender_desc} patient"
        
        # 根据患者描述添加细节
        if patient.get("description"):
            base_prompt += f", {patient['description']}"
        
        # 添加风格描述
        style_prompts = {
            "realistic": "photorealistic, high quality, professional lighting",
            "cartoon": "cartoon style, friendly, approachable",
            "anime": "anime style, detailed, expressive"
        }
        
        full_prompt = f"{base_prompt}, {style_prompts.get(request.style, style_prompts['realistic'])}"
        
        # 负面prompt
        negative_prompt = "blurry, low quality, distorted, ugly, inappropriate"
        
        # 生成图像
        with torch.autocast(device):
            image = pipe(
                prompt=full_prompt,
                negative_prompt=negative_prompt,
                num_inference_steps=50,
                guidance_scale=7.5,
                width=512,
                height=512
            ).images[0]
        
        # 保存图像
        image_id = str(uuid.uuid4())
        image_path = f"/app/storage/avatars/{image_id}.png"
        os.makedirs(os.path.dirname(image_path), exist_ok=True)
        image.save(image_path)
        
        # 返回URL
        avatar_url = f"/static/avatars/{image_id}.png"
        
        return {
            "avatar_url": avatar_url,
            "image_id": image_id,
            "prompt_used": full_prompt
        }
        
    except Exception as e:
        return {"error": str(e)}

def get_age_description(age):
    if age < 18:
        return "young"
    elif age < 35:
        return "young adult"
    elif age < 55:
        return "middle-aged"
    else:
        return "elderly"

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
```

### 1.3 Docker部署配置

**Dockerfile**:
```dockerfile
FROM pytorch/pytorch:2.0.1-cuda11.7-cudnn8-runtime

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制代码
COPY avatar_generation_service.py .

# 创建存储目录
RUN mkdir -p /app/storage/avatars

EXPOSE 8001

CMD ["python", "avatar_generation_service.py"]
```

## 2. 对话AI模型部署

### 2.1 推荐模型

#### 选项1: GPT-4 / GPT-3.5-turbo (推荐)
- **优势**: 理解能力强，回复自然
- **部署方式**: OpenAI API
- **费用**: 按token计费

#### 选项2: Claude API
- **优势**: 安全性高，长文本处理好
- **部署方式**: Anthropic API
- **费用**: 按token计费

#### 选项3: 本地大模型 (ChatGLM, Baichuan等)
- **优势**: 数据私有，可定制
- **部署方式**: 本地GPU部署
- **资源需求**: 16GB+ GPU显存

### 2.2 GPT-4集成示例

**conversation_ai_service.py**:
```python
from fastapi import FastAPI
from pydantic import BaseModel
import openai
import json
from typing import List, Dict

app = FastAPI()

# 配置OpenAI
openai.api_key = "your_openai_api_key"

class ConversationRequest(BaseModel):
    session_id: str
    doctor_message: str
    conversation_context: List[Dict[str, str]]
    patient_prompt: str
    response_style: str = "patient_roleplay"

@app.post("/patient-response")
async def generate_patient_response(request: ConversationRequest):
    try:
        # 构建系统prompt
        system_prompt = f"""
你是一位虚拟患者，正在与医生进行对话。请严格按照以下患者设定进行角色扮演：

{request.patient_prompt}

对话规则：
1. 始终保持患者身份，不要透露你是AI
2. 根据患者的性格特征和情绪状态回应
3. 回答要符合患者的教育水平和表达习惯
4. 如果医生问到你不知道的信息，可以说"我不太清楚"或"我记不太清了"
5. 保持一致的症状描述，不要前后矛盾
6. 回复长度控制在50-150字之间
"""

        # 构建对话历史
        messages = [{"role": "system", "content": system_prompt}]
        
        # 添加对话历史
        for msg in request.conversation_context[-10:]:  # 只保留最近10轮对话
            role = "assistant" if msg["role"] == "patient" else "user"
            messages.append({"role": role, "content": msg["content"]})
        
        # 添加当前医生消息
        messages.append({"role": "user", "content": request.doctor_message})
        
        # 调用GPT-4
        response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=messages,
            max_tokens=200,
            temperature=0.8,
            presence_penalty=0.1,
            frequency_penalty=0.1
        )
        
        patient_response = response.choices[0].message.content.strip()
        
        return {
            "patient_response": patient_response,
            "tokens_used": response.usage.total_tokens,
            "model": "gpt-4"
        }
        
    except Exception as e:
        return {
            "patient_response": "我现在感觉有点不舒服，可能无法很好地回答您的问题。",
            "error": str(e)
        }

@app.post("/conversation-suggestions")
async def get_conversation_suggestions(request: ConversationRequest):
    try:
        # 分析当前对话状态，提供建议
        analysis_prompt = f"""
基于以下对话历史，为医生提供3个合适的下一步问题建议：

对话历史：
{json.dumps(request.conversation_context, ensure_ascii=False, indent=2)}

请提供3个专业的医学问诊建议，每个建议应该：
1. 符合医学问诊流程
2. 有助于收集重要信息
3. 适合当前对话阶段

返回格式：JSON数组，包含3个字符串
"""

        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": analysis_prompt}],
            max_tokens=300,
            temperature=0.3
        )
        
        suggestions_text = response.choices[0].message.content.strip()
        
        # 尝试解析JSON，如果失败则返回默认建议
        try:
            suggestions = json.loads(suggestions_text)
        except:
            suggestions = [
                "请详细描述一下您的症状",
                "这种情况持续多长时间了？",
                "您之前有过类似的经历吗？"
            ]
        
        return {
            "suggestions": suggestions[:3],  # 确保只返回3个建议
            "model": "gpt-3.5-turbo"
        }
        
    except Exception as e:
        return {
            "suggestions": [
                "请告诉我您的主要不适",
                "症状什么时候开始的？",
                "还有其他伴随症状吗？"
            ],
            "error": str(e)
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
```

## 3. 分析AI模型部署

### 3.1 对话分析模型

**analysis_ai_service.py**:
```python
from fastapi import FastAPI
from pydantic import BaseModel
import openai
import json
from typing import Dict, List

app = FastAPI()

class AnalysisRequest(BaseModel):
    session_id: str
    conversation_data: Dict
    patient_info: Dict
    analysis_dimensions: List[str]

@app.post("/analyze-conversation")
async def analyze_conversation(request: AnalysisRequest):
    try:
        # 构建分析prompt
        analysis_prompt = f"""
请对以下医患对话进行专业分析，从以下5个维度进行评估：

1. 给予的情绪价值 (0-100分)
2. 医生诊断的正确性 (0-100分)
3. 分配科室的正确性 (0-100分)
4. 对话质量分析
5. 总体建议和评分

患者信息：
{json.dumps(request.patient_info, ensure_ascii=False, indent=2)}

对话记录：
{json.dumps(request.conversation_data, ensure_ascii=False, indent=2)}

请返回详细的JSON格式分析结果，包含每个维度的得分、详细反馈和改进建议。
"""

        response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[{"role": "user", "content": analysis_prompt}],
            max_tokens=2000,
            temperature=0.3
        )
        
        analysis_text = response.choices[0].message.content.strip()
        
        # 尝试解析JSON结果
        try:
            analysis_result = json.loads(analysis_text)
        except:
            # 如果解析失败，返回默认结构
            analysis_result = {
                "emotional_value_score": 75.0,
                "diagnosis_accuracy_score": 80.0,
                "department_accuracy_score": 85.0,
                "conversation_quality": 78.0,
                "overall_score": 79.5,
                "detailed_feedback": "分析结果解析失败，请检查模型输出格式"
            }
        
        return analysis_result
        
    except Exception as e:
        return {
            "error": str(e),
            "emotional_value_score": 70.0,
            "diagnosis_accuracy_score": 70.0,
            "department_accuracy_score": 70.0,
            "conversation_quality": 70.0,
            "overall_score": 70.0
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)
```

## 4. 诊断评估AI模型

### 4.1 医疗知识图谱集成

**diagnosis_ai_service.py**:
```python
from fastapi import FastAPI
from pydantic import BaseModel
import openai
import json
from typing import Dict

app = FastAPI()

class DiagnosisRequest(BaseModel):
    doctor_diagnosis: str
    correct_diagnosis: str
    conversation_data: Dict
    symptoms: list
    patient_info: Dict

@app.post("/evaluate-diagnosis")
async def evaluate_diagnosis(request: DiagnosisRequest):
    try:
        # 构建诊断评估prompt
        evaluation_prompt = f"""
作为医学专家，请评估医生的诊断准确性：

正确诊断：{request.correct_diagnosis}
医生诊断：{request.doctor_diagnosis}
患者症状：{request.symptoms}
对话过程：{json.dumps(request.conversation_data, ensure_ascii=False)}

请从以下方面进行评估：
1. 诊断准确性 (0-100分)
2. 诊断推理过程质量 (0-100分)
3. 症状识别完整性 (0-100分)
4. 遗漏的关键信息
5. 诊断改进建议

返回JSON格式的详细评估结果。
"""

        response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[{"role": "user", "content": evaluation_prompt}],
            max_tokens=1500,
            temperature=0.2
        )
        
        evaluation_text = response.choices[0].message.content.strip()
        
        try:
            evaluation_result = json.loads(evaluation_text)
        except:
            # 简单的相似度计算作为备选
            accuracy = calculate_diagnosis_similarity(
                request.doctor_diagnosis, 
                request.correct_diagnosis
            )
            
            evaluation_result = {
                "accuracy_score": accuracy,
                "reasoning_quality": 75.0,
                "completeness": 70.0,
                "missed_information": ["需要更详细的病史询问"],
                "recommendations": ["建议进行更系统的问诊"]
            }
        
        return evaluation_result
        
    except Exception as e:
        return {
            "error": str(e),
            "accuracy_score": 70.0,
            "reasoning_quality": 70.0,
            "completeness": 70.0
        }

def calculate_diagnosis_similarity(diagnosis1: str, diagnosis2: str) -> float:
    """简单的诊断相似度计算"""
    if not diagnosis1 or not diagnosis2:
        return 0.0
    
    # 转换为小写并移除标点
    d1 = diagnosis1.lower().replace("，", "").replace("。", "")
    d2 = diagnosis2.lower().replace("，", "").replace("。", "")
    
    if d1 == d2:
        return 100.0
    elif d1 in d2 or d2 in d1:
        return 80.0
    else:
        # 可以实现更复杂的相似度算法
        return 60.0

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8004)
```

## 5. 统一部署配置

### 5.1 Docker Compose

**docker-compose.yml**:
```yaml
version: '3.8'

services:
  avatar-generator:
    build: ./avatar
    ports:
      - "8001:8001"
    volumes:
      - ./storage/avatars:/app/storage/avatars
    environment:
      - CUDA_VISIBLE_DEVICES=0
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  conversation-ai:
    build: ./conversation
    ports:
      - "8002:8002"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MODEL_NAME=gpt-4

  analysis-ai:
    build: ./analysis
    ports:
      - "8003:8003"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}

  diagnosis-ai:
    build: ./diagnosis
    ports:
      - "8004:8004"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  redis_data:
```

### 5.2 环境变量配置

**.env**:
```bash
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_ORG_ID=your_org_id

# 模型配置
AVATAR_MODEL_PATH=./models/stable-diffusion
CONVERSATION_MODEL=gpt-4
ANALYSIS_MODEL=gpt-4

# 服务配置
AVATAR_SERVICE_URL=http://localhost:8001
CONVERSATION_SERVICE_URL=http://localhost:8002
ANALYSIS_SERVICE_URL=http://localhost:8003
DIAGNOSIS_SERVICE_URL=http://localhost:8004

# 缓存配置
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600
```

## 6. 性能优化

### 6.1 模型缓存
- 实现模型预加载
- 使用Redis缓存常用结果
- 批处理请求优化

### 6.2 负载均衡
- 多实例部署
- 请求队列管理
- 自动扩缩容

### 6.3 监控告警
- API响应时间监控
- 模型推理性能监控
- 错误率统计和告警

通过以上配置，可以构建一个完整的AI模型服务体系，为虚拟患者对话系统提供智能化的交互和分析能力。
