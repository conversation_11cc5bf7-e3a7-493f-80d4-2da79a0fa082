from typing import List, Optional, Dict, Any
import random
import uuid
from datetime import datetime
import logging

from app.models.virtual_patient import (
    VirtualPatientPrompt, PatientGender, PatientAgeGroup, 
    DepartmentType, SeverityLevel, PatientGenerationResponse
)
from app.services.database import get_database

logger = logging.getLogger(__name__)

class VirtualPatientService:
    """虚拟患者服务类"""
    
    def __init__(self):
        self.db = None
    
    async def _get_db(self):
        """获取数据库连接"""
        if not self.db:
            db_service = await get_database()
            self.db = db_service.database
        return self.db
    
    async def create_sample_patients(self):
        """创建示例患者数据"""
        db = await self._get_db()
        
        sample_patients = [
            {
                "patient_id": str(uuid.uuid4()),
                "name": "张小明",
                "age": 28,
                "gender": PatientGender.MALE,
                "age_group": PatientAgeGroup.YOUNG_ADULT,
                "occupation": "程序员",
                "education_level": "本科",
                "marital_status": "未婚",
                "chief_complaint": "最近工作压力大，经常失眠，感觉很疲惫",
                "present_illness": "近2个月来工作压力增大，每天加班到很晚，晚上躺在床上难以入睡，即使睡着了也容易醒来，白天感觉疲惫不堪，注意力难以集中。",
                "past_medical_history": "既往体健，无重大疾病史",
                "family_history": "父亲有高血压病史",
                "symptoms": ["失眠", "疲惫", "注意力不集中", "工作压力大"],
                "symptom_duration": "2个月",
                "severity": SeverityLevel.MILD,
                "correct_diagnosis": "工作压力相关的睡眠障碍",
                "correct_department": DepartmentType.PSYCHIATRY,
                "personality_traits": ["内向", "完美主义", "责任心强"],
                "communication_style": "比较内敛，需要医生主动询问才会详细描述症状",
                "emotional_state": "焦虑、疲惫",
                "cooperation_level": "配合度较高",
                "system_prompt": "你是一个28岁的程序员张小明，最近因为工作压力大导致失眠。你性格内向，不太主动表达，需要医生询问才会详细说明症状。你对自己的健康状况有些担心，希望能得到专业的建议。",
                "conversation_guidelines": [
                    "不要主动提供过多信息，等医生询问",
                    "表现出一定的焦虑和疲惫",
                    "对工作压力的描述要具体",
                    "对睡眠问题要详细说明"
                ],
                "avatar_description": "年轻男性，略显疲惫，穿着休闲",
                "voice_characteristics": "声音略显疲惫，语速适中",
                "difficulty_level": 2,
                "tags": ["失眠", "工作压力", "年轻人", "程序员"],
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            {
                "patient_id": str(uuid.uuid4()),
                "name": "李阿姨",
                "age": 55,
                "gender": PatientGender.FEMALE,
                "age_group": PatientAgeGroup.MIDDLE_AGED,
                "occupation": "退休教师",
                "education_level": "本科",
                "marital_status": "已婚",
                "chief_complaint": "胸闷气短，心慌，担心是心脏病",
                "present_illness": "近1个月来经常感到胸闷气短，特别是爬楼梯时更明显，有时会心慌，担心自己得了心脏病，晚上经常因为担心而睡不着。",
                "past_medical_history": "有轻度高血压，正在服用降压药",
                "family_history": "母亲有冠心病史",
                "symptoms": ["胸闷", "气短", "心慌", "焦虑", "失眠"],
                "symptom_duration": "1个月",
                "severity": SeverityLevel.MODERATE,
                "correct_diagnosis": "焦虑症伴躯体化症状",
                "correct_department": DepartmentType.PSYCHIATRY,
                "personality_traits": ["敏感", "容易担心", "善于表达"],
                "communication_style": "比较健谈，会主动描述症状，但容易紧张",
                "emotional_state": "焦虑、担心",
                "cooperation_level": "配合度高，但容易紧张",
                "system_prompt": "你是一个55岁的退休教师李阿姨，最近出现胸闷气短等症状，非常担心自己得了心脏病。你比较健谈，会主动描述症状，但容易紧张和担心。",
                "conversation_guidelines": [
                    "主动描述症状，但表现出担心和焦虑",
                    "经常询问是否是心脏病",
                    "对家族病史比较敏感",
                    "希望得到医生的安慰和解释"
                ],
                "avatar_description": "中年女性，表情略显担忧",
                "voice_characteristics": "语速较快，带有一些紧张感",
                "difficulty_level": 3,
                "tags": ["焦虑症", "躯体化症状", "中年女性", "退休"],
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
        ]
        
        try:
            # 检查是否已存在示例数据
            existing_count = await db.virtual_patients.count_documents({})
            if existing_count == 0:
                await db.virtual_patients.insert_many(sample_patients)
                logger.info(f"成功创建 {len(sample_patients)} 个示例患者")
            else:
                logger.info(f"数据库中已存在 {existing_count} 个患者，跳过示例数据创建")
                
        except Exception as e:
            logger.error(f"创建示例患者数据失败: {str(e)}")
            raise
    
    async def get_random_patient(self, difficulty_level: Optional[int] = None) -> Optional[VirtualPatientPrompt]:
        """随机获取一个虚拟患者"""
        try:
            db = await self._get_db()
            
            # 构建查询条件
            query = {}
            if difficulty_level:
                query["difficulty_level"] = difficulty_level
            
            # 随机获取一个患者
            pipeline = [
                {"$match": query},
                {"$sample": {"size": 1}}
            ]
            
            cursor = db.virtual_patients.aggregate(pipeline)
            patient_data = await cursor.to_list(length=1)
            
            if patient_data:
                patient_dict = patient_data[0]
                # 移除MongoDB的_id字段
                patient_dict.pop('_id', None)
                return VirtualPatientPrompt(**patient_dict)
            else:
                logger.warning("没有找到符合条件的患者")
                return None
                
        except Exception as e:
            logger.error(f"获取随机患者失败: {str(e)}")
            raise
    
    async def get_random_patient_prompt(
        self,
        difficulty_level: Optional[int] = None,
        department_preference: Optional[DepartmentType] = None,
        age_group_preference: Optional[PatientAgeGroup] = None,
        gender_preference: Optional[PatientGender] = None,
        severity_preference: Optional[SeverityLevel] = None,
        tags: Optional[List[str]] = None
    ) -> Optional[VirtualPatientPrompt]:
        """随机获取患者prompt - 新接口"""
        try:
            db = await self._get_db()

            # 构建查询条件
            query = {}
            if difficulty_level:
                query["difficulty_level"] = difficulty_level
            if department_preference:
                query["correct_department"] = department_preference.value
            if age_group_preference:
                query["age_group"] = age_group_preference.value
            if gender_preference:
                query["gender"] = gender_preference.value
            if severity_preference:
                query["severity"] = severity_preference.value
            if tags:
                query["tags"] = {"$in": tags}

            # 随机获取一个患者
            pipeline = [
                {"$match": query},
                {"$sample": {"size": 1}}
            ]

            cursor = db.virtual_patients.aggregate(pipeline)
            patient_data = await cursor.to_list(length=1)

            if patient_data:
                patient_dict = patient_data[0]
                # 移除MongoDB的_id字段
                patient_dict.pop('_id', None)
                return VirtualPatientPrompt(**patient_dict)
            else:
                logger.warning("没有找到符合条件的患者")
                return None

        except Exception as e:
            logger.error(f"获取随机患者prompt失败: {str(e)}")
            return None

    async def get_patient_prompts(
        self,
        department: Optional[DepartmentType] = None,
        difficulty_level: Optional[int] = None,
        age_group: Optional[PatientAgeGroup] = None,
        gender: Optional[PatientGender] = None,
        severity: Optional[SeverityLevel] = None,
        limit: int = 10,
        offset: int = 0
    ) -> List[VirtualPatientPrompt]:
        """获取患者prompt列表"""
        try:
            db = await self._get_db()

            # 构建查询条件
            query = {}
            if department:
                query["correct_department"] = department.value
            if difficulty_level:
                query["difficulty_level"] = difficulty_level
            if age_group:
                query["age_group"] = age_group.value
            if gender:
                query["gender"] = gender.value
            if severity:
                query["severity"] = severity.value

            # 查询数据
            cursor = db.virtual_patients.find(query).skip(offset).limit(limit)
            patients = await cursor.to_list(length=limit)

            # 转换为Pydantic模型
            result = []
            for patient in patients:
                patient.pop('_id', None)
                result.append(VirtualPatientPrompt(**patient))

            return result

        except Exception as e:
            logger.error(f"获取患者prompt列表失败: {str(e)}")
            return []

    async def create_patient_prompt(self, prompt: VirtualPatientPrompt) -> VirtualPatientPrompt:
        """创建新的患者prompt"""
        try:
            db = await self._get_db()

            # 生成ID
            prompt.id = str(uuid.uuid4())
            prompt.created_at = datetime.now()
            prompt.updated_at = datetime.now()

            # 插入数据
            await db.virtual_patients.insert_one(prompt.dict())

            return prompt

        except Exception as e:
            logger.error(f"创建患者prompt失败: {str(e)}")
            raise

    async def get_patient_prompt_by_id(self, prompt_id: str) -> Optional[VirtualPatientPrompt]:
        """根据ID获取患者prompt"""
        try:
            db = await self._get_db()

            patient_data = await db.virtual_patients.find_one({"id": prompt_id})

            if patient_data:
                patient_data.pop('_id', None)
                return VirtualPatientPrompt(**patient_data)
            return None

        except Exception as e:
            logger.error(f"获取患者prompt失败: {str(e)}")
            return None

    async def update_patient_prompt(self, prompt_id: str, prompt: VirtualPatientPrompt) -> Optional[VirtualPatientPrompt]:
        """更新患者prompt"""
        try:
            db = await self._get_db()

            prompt.updated_at = datetime.now()

            result = await db.virtual_patients.update_one(
                {"id": prompt_id},
                {"$set": prompt.dict(exclude={"id", "created_at"})}
            )

            if result.modified_count > 0:
                return await self.get_patient_prompt_by_id(prompt_id)
            return None

        except Exception as e:
            logger.error(f"更新患者prompt失败: {str(e)}")
            return None

    async def delete_patient_prompt(self, prompt_id: str) -> bool:
        """删除患者prompt"""
        try:
            db = await self._get_db()

            result = await db.virtual_patients.delete_one({"id": prompt_id})

            return result.deleted_count > 0

        except Exception as e:
            logger.error(f"删除患者prompt失败: {str(e)}")
            return False

    async def generate_patient_session(self, difficulty_level: Optional[int] = None) -> PatientGenerationResponse:
        """生成患者会话"""
        try:
            # 获取随机患者
            patient = await self.get_random_patient(difficulty_level)
            if not patient:
                raise Exception("无法获取患者数据")

            # 生成会话ID
            session_id = str(uuid.uuid4())

            # TODO: 这里应该调用数字人生成API
            avatar_url = None  # 预留接口

            # TODO: 这里应该配置语音参数
            voice_config = {
                "voice_id": "default",
                "speed": 1.0,
                "pitch": 1.0,
                "language": "zh-CN"
            }

            return PatientGenerationResponse(
                patient=patient,
                session_id=session_id,
                avatar_url=avatar_url,
                voice_config=voice_config
            )

        except Exception as e:
            logger.error(f"生成患者会话失败: {str(e)}")
            raise

# 全局服务实例
virtual_patient_service = VirtualPatientService()
