from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional
import uuid
import logging

from ..models.virtual_patient import (
    VirtualPatientPrompt, 
    PatientGenerationRequest, 
    PatientGenerationResponse,
    DepartmentType,
    PatientAgeGroup,
    PatientGender,
    SeverityLevel
)
from ..services.virtual_patient_service import virtual_patient_service
from ..services.ai_service import ai_service

router = APIRouter(prefix="/virtual-patient", tags=["虚拟患者"])
logger = logging.getLogger(__name__)

@router.post("/generate", response_model=PatientGenerationResponse)
async def generate_patient(request: PatientGenerationRequest = None):
    """
    生成虚拟患者
    从数据库中随机选择prompt生成虚拟患者
    """
    try:
        # 如果没有提供请求参数，使用默认值
        if request is None:
            request = PatientGenerationRequest()
        
        # 从数据库随机选择患者prompt
        patient_prompt = await virtual_patient_service.get_random_patient_prompt(
            difficulty_level=request.difficulty_level,
            department_preference=request.department_preference,
            age_group_preference=request.age_group_preference,
            gender_preference=request.gender_preference,
            severity_preference=request.severity_preference,
            tags=request.tags
        )
        
        if not patient_prompt:
            raise HTTPException(status_code=404, detail="未找到符合条件的患者模板")
        
        # 生成会话ID
        session_id = str(uuid.uuid4())
        
        # 生成数字人头像（调用AI服务）
        avatar_url = await ai_service.generate_avatar(patient_prompt)
        
        # 配置语音参数
        voice_config = await ai_service.get_voice_config(patient_prompt)
        
        return PatientGenerationResponse(
            patient=patient_prompt,
            session_id=session_id,
            avatar_url=avatar_url,
            voice_config=voice_config
        )
        
    except Exception as e:
        logger.error(f"生成虚拟患者失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成虚拟患者失败: {str(e)}")

@router.get("/prompts", response_model=List[VirtualPatientPrompt])
async def get_patient_prompts(
    department: Optional[DepartmentType] = None,
    difficulty_level: Optional[int] = None,
    age_group: Optional[PatientAgeGroup] = None,
    gender: Optional[PatientGender] = None,
    severity: Optional[SeverityLevel] = None,
    limit: int = 10,
    offset: int = 0
):
    """
    获取患者prompt列表
    """
    try:
        prompts = await virtual_patient_service.get_patient_prompts(
            department=department,
            difficulty_level=difficulty_level,
            age_group=age_group,
            gender=gender,
            severity=severity,
            limit=limit,
            offset=offset
        )
        return prompts
    except Exception as e:
        logger.error(f"获取患者prompt列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取患者prompt列表失败: {str(e)}")

@router.post("/prompts", response_model=VirtualPatientPrompt)
async def create_patient_prompt(prompt: VirtualPatientPrompt):
    """
    创建新的患者prompt
    """
    try:
        created_prompt = await virtual_patient_service.create_patient_prompt(prompt)
        return created_prompt
    except Exception as e:
        logger.error(f"创建患者prompt失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建患者prompt失败: {str(e)}")

@router.get("/prompts/{prompt_id}", response_model=VirtualPatientPrompt)
async def get_patient_prompt(prompt_id: str):
    """
    获取特定的患者prompt
    """
    try:
        prompt = await virtual_patient_service.get_patient_prompt_by_id(prompt_id)
        if not prompt:
            raise HTTPException(status_code=404, detail="患者prompt不存在")
        return prompt
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取患者prompt失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取患者prompt失败: {str(e)}")

@router.put("/prompts/{prompt_id}", response_model=VirtualPatientPrompt)
async def update_patient_prompt(prompt_id: str, prompt: VirtualPatientPrompt):
    """
    更新患者prompt
    """
    try:
        updated_prompt = await virtual_patient_service.update_patient_prompt(prompt_id, prompt)
        if not updated_prompt:
            raise HTTPException(status_code=404, detail="患者prompt不存在")
        return updated_prompt
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新患者prompt失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新患者prompt失败: {str(e)}")

@router.delete("/prompts/{prompt_id}")
async def delete_patient_prompt(prompt_id: str):
    """
    删除患者prompt
    """
    try:
        success = await virtual_patient_service.delete_patient_prompt(prompt_id)
        if not success:
            raise HTTPException(status_code=404, detail="患者prompt不存在")
        return {"message": "患者prompt删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除患者prompt失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除患者prompt失败: {str(e)}")

@router.get("/departments", response_model=List[str])
async def get_departments():
    """
    获取所有可用科室列表
    """
    return [dept.value for dept in DepartmentType]

@router.get("/age-groups", response_model=List[str])
async def get_age_groups():
    """
    获取所有年龄组列表
    """
    return [age.value for age in PatientAgeGroup]

@router.get("/genders", response_model=List[str])
async def get_genders():
    """
    获取所有性别选项
    """
    return [gender.value for gender in PatientGender]

@router.get("/severity-levels", response_model=List[str])
async def get_severity_levels():
    """
    获取所有严重程度级别
    """
    return [severity.value for severity in SeverityLevel]
