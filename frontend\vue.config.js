module.exports = {
  pluginOptions: {
    electronBuilder: {
      nodeIntegration: true,
      builderOptions: {
        // 配置打包选项
        appId: 'com.virtual-patient-system.app',
        productName: '虚拟患者对话系统',
        copyright: 'Copyright © 2025',
        // 包含后端文件
        extraResources: [
          {
            from: '../backend/dist/virtual-patient-system.exe',
            to: 'backend/virtual-patient-system.exe'
          },
          {
            from: '../backend/templates',
            to: 'backend/templates'
          }
        ],
        // 支持Windows和Linux
        win: {
          icon: './public/icon.png',
          target: [
            {
              target: 'nsis',
              arch: ['x64']
            }
          ]
        },
        linux: {
          icon: './public/icon.png',
          target: [
            {
              target: 'AppImage',
              arch: ['x64']
            },
            {
              target: 'deb',
              arch: ['x64']
            }
          ],
          category: 'Utility'
        },
        // 配置自动更新
        publish: ['github'],
        // 打包后的文件名格式
        artifactName: '${productName}-${version}-${os}-${arch}.${ext}'
      },
      // 主进程文件
      mainProcessFile: 'src/background.js',
      // 渲染进程文件
      rendererProcessFile: 'src/main.js'
    }
  },
  // 开发服务器配置
  devServer: {
    port: 8081,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  }
};