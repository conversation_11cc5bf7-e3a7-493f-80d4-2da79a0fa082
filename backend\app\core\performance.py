"""
性能优化配置

包含缓存、连接池、异步优化等性能相关配置
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional, Callable
from functools import wraps
from datetime import datetime, timedelta
import hashlib
import json

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {}
        self.request_times = []
        self.error_counts = {}
        
    def record_request_time(self, endpoint: str, duration: float):
        """记录请求时间"""
        if endpoint not in self.metrics:
            self.metrics[endpoint] = {
                "total_requests": 0,
                "total_time": 0,
                "avg_time": 0,
                "max_time": 0,
                "min_time": float('inf')
            }
        
        metrics = self.metrics[endpoint]
        metrics["total_requests"] += 1
        metrics["total_time"] += duration
        metrics["avg_time"] = metrics["total_time"] / metrics["total_requests"]
        metrics["max_time"] = max(metrics["max_time"], duration)
        metrics["min_time"] = min(metrics["min_time"], duration)
        
        # 保留最近1000次请求时间
        self.request_times.append({
            "endpoint": endpoint,
            "duration": duration,
            "timestamp": datetime.now()
        })
        
        if len(self.request_times) > 1000:
            self.request_times = self.request_times[-1000:]
    
    def record_error(self, endpoint: str, error_type: str):
        """记录错误"""
        key = f"{endpoint}:{error_type}"
        self.error_counts[key] = self.error_counts.get(key, 0) + 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return {
            "endpoint_metrics": self.metrics,
            "error_counts": self.error_counts,
            "recent_requests": len(self.request_times),
            "system_health": self._calculate_system_health()
        }
    
    def _calculate_system_health(self) -> Dict[str, Any]:
        """计算系统健康度"""
        if not self.request_times:
            return {"status": "unknown", "score": 0}
        
        # 计算最近5分钟的平均响应时间
        five_minutes_ago = datetime.now() - timedelta(minutes=5)
        recent_requests = [
            req for req in self.request_times 
            if req["timestamp"] > five_minutes_ago
        ]
        
        if not recent_requests:
            return {"status": "idle", "score": 100}
        
        avg_response_time = sum(req["duration"] for req in recent_requests) / len(recent_requests)
        
        # 健康度评分
        if avg_response_time < 0.5:
            status = "excellent"
            score = 100
        elif avg_response_time < 1.0:
            status = "good"
            score = 80
        elif avg_response_time < 2.0:
            status = "fair"
            score = 60
        elif avg_response_time < 5.0:
            status = "poor"
            score = 40
        else:
            status = "critical"
            score = 20
        
        return {
            "status": status,
            "score": score,
            "avg_response_time": avg_response_time,
            "recent_request_count": len(recent_requests)
        }

# 全局性能监控器
performance_monitor = PerformanceMonitor()

class MemoryCache:
    """内存缓存"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        self.cache = {}
        self.access_times = {}
        self.max_size = max_size
        self.ttl = ttl  # 生存时间（秒）
    
    def _generate_key(self, key: str) -> str:
        """生成缓存键"""
        if isinstance(key, dict):
            key = json.dumps(key, sort_keys=True)
        return hashlib.md5(key.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        cache_key = self._generate_key(key)
        
        if cache_key not in self.cache:
            return None
        
        # 检查是否过期
        cached_time, value = self.cache[cache_key]
        if time.time() - cached_time > self.ttl:
            del self.cache[cache_key]
            if cache_key in self.access_times:
                del self.access_times[cache_key]
            return None
        
        # 更新访问时间
        self.access_times[cache_key] = time.time()
        return value
    
    def set(self, key: str, value: Any):
        """设置缓存值"""
        cache_key = self._generate_key(key)
        current_time = time.time()
        
        # 如果缓存已满，删除最久未访问的项
        if len(self.cache) >= self.max_size and cache_key not in self.cache:
            self._evict_lru()
        
        self.cache[cache_key] = (current_time, value)
        self.access_times[cache_key] = current_time
    
    def delete(self, key: str):
        """删除缓存值"""
        cache_key = self._generate_key(key)
        if cache_key in self.cache:
            del self.cache[cache_key]
        if cache_key in self.access_times:
            del self.access_times[cache_key]
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_times.clear()
    
    def _evict_lru(self):
        """删除最久未访问的项"""
        if not self.access_times:
            return
        
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        del self.cache[lru_key]
        del self.access_times[lru_key]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            "size": len(self.cache),
            "max_size": self.max_size,
            "ttl": self.ttl,
            "hit_rate": getattr(self, '_hit_count', 0) / max(getattr(self, '_total_requests', 1), 1)
        }

# 全局缓存实例
memory_cache = MemoryCache()

def performance_timer(func: Callable) -> Callable:
    """性能计时装饰器"""
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        endpoint = f"{func.__module__}.{func.__name__}"
        
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            performance_monitor.record_request_time(endpoint, duration)
            
            if duration > 5.0:  # 超过5秒的请求记录警告
                logger.warning(f"慢请求: {endpoint} 耗时 {duration:.2f}秒")
            
            return result
        except Exception as e:
            duration = time.time() - start_time
            performance_monitor.record_request_time(endpoint, duration)
            performance_monitor.record_error(endpoint, type(e).__name__)
            raise
    
    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        endpoint = f"{func.__module__}.{func.__name__}"
        
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            performance_monitor.record_request_time(endpoint, duration)
            
            if duration > 5.0:
                logger.warning(f"慢请求: {endpoint} 耗时 {duration:.2f}秒")
            
            return result
        except Exception as e:
            duration = time.time() - start_time
            performance_monitor.record_request_time(endpoint, duration)
            performance_monitor.record_error(endpoint, type(e).__name__)
            raise
    
    return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

def cache_result(ttl: int = 3600, key_func: Optional[Callable] = None):
    """结果缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 尝试从缓存获取
            cached_result = memory_cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            memory_cache.set(cache_key, result)
            
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 尝试从缓存获取
            cached_result = memory_cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            memory_cache.set(cache_key, result)
            
            return result
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator

class ConnectionPool:
    """连接池管理"""
    
    def __init__(self, max_connections: int = 10):
        self.max_connections = max_connections
        self.active_connections = 0
        self.semaphore = asyncio.Semaphore(max_connections)
    
    async def acquire(self):
        """获取连接"""
        await self.semaphore.acquire()
        self.active_connections += 1
    
    def release(self):
        """释放连接"""
        if self.active_connections > 0:
            self.active_connections -= 1
            self.semaphore.release()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计"""
        return {
            "max_connections": self.max_connections,
            "active_connections": self.active_connections,
            "available_connections": self.max_connections - self.active_connections
        }

# 全局连接池
connection_pool = ConnectionPool()

class RateLimiter:
    """速率限制器"""
    
    def __init__(self, max_requests: int = 100, time_window: int = 60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = {}
    
    def is_allowed(self, identifier: str) -> bool:
        """检查是否允许请求"""
        current_time = time.time()
        
        if identifier not in self.requests:
            self.requests[identifier] = []
        
        # 清理过期的请求记录
        self.requests[identifier] = [
            req_time for req_time in self.requests[identifier]
            if current_time - req_time < self.time_window
        ]
        
        # 检查是否超过限制
        if len(self.requests[identifier]) >= self.max_requests:
            return False
        
        # 记录当前请求
        self.requests[identifier].append(current_time)
        return True
    
    def get_remaining_requests(self, identifier: str) -> int:
        """获取剩余请求数"""
        current_time = time.time()
        
        if identifier not in self.requests:
            return self.max_requests
        
        # 清理过期的请求记录
        self.requests[identifier] = [
            req_time for req_time in self.requests[identifier]
            if current_time - req_time < self.time_window
        ]
        
        return max(0, self.max_requests - len(self.requests[identifier]))

# 全局速率限制器
rate_limiter = RateLimiter()

def optimize_database_query(query_func: Callable) -> Callable:
    """数据库查询优化装饰器"""
    @wraps(query_func)
    async def wrapper(*args, **kwargs):
        # 添加查询超时
        try:
            result = await asyncio.wait_for(query_func(*args, **kwargs), timeout=30.0)
            return result
        except asyncio.TimeoutError:
            logger.error(f"数据库查询超时: {query_func.__name__}")
            raise Exception("数据库查询超时")
    
    return wrapper

def get_performance_report() -> Dict[str, Any]:
    """获取性能报告"""
    return {
        "performance_metrics": performance_monitor.get_metrics(),
        "cache_stats": memory_cache.get_stats(),
        "connection_pool_stats": connection_pool.get_stats(),
        "timestamp": datetime.now().isoformat()
    }
