@echo off
echo ========================================
echo 虚拟患者对话系统 - 开发环境启动
echo ========================================

echo.
echo 1. 启动后端服务...
cd backend
start "后端服务" cmd /k ".venv\Scripts\activate && python -m uvicorn app.main:app --host 127.0.0.1 --port 8000 --reload"

echo.
echo 2. 等待后端启动...
timeout /t 3 /nobreak > nul

echo.
echo 3. 启动 Electron 开发环境...
cd ..\frontend
call npm run electron:serve

echo.
echo 开发环境启动完成！
pause
