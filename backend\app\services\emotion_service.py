from typing import Dict, List, Any
import random

def analyze_emotion(text: str) -> Dict[str, Any]:
    """
    分析文本中的情绪
    
    参数:
        text: 用户输入的文本内容
        
    返回:
        包含情绪分析结果的字典
    
    注意: 这是一个模拟实现，实际项目中应该集成真实的情绪分析模型
    TODO: 集成实际的情绪分析模型
    """
    # 模拟情绪分析结果
    # 在实际项目中，这里应该调用NLP模型进行分析
    
    # 模拟情绪值分布
    positive = random.randint(20, 40)
    negative = random.randint(30, 50)
    neutral = 100 - positive - negative
    
    # 根据文本长度和内容简单判断
    text_length = len(text)
    has_negative_words = any(word in text.lower() for word in ["压力", "疲惫", "焦虑", "失眠", "痛苦", "难过"])
    has_positive_words = any(word in text.lower() for word in ["开心", "快乐", "满意", "喜欢", "享受", "感谢"])
    
    # 根据简单规则调整情绪值
    if has_negative_words:
        negative = max(negative, 45)
        positive = min(positive, 30)
    
    if has_positive_words:
        positive = max(positive, 40)
        negative = min(negative, 30)
    
    # 确保总和为100
    total = positive + negative + neutral
    positive = int((positive / total) * 100)
    negative = int((negative / total) * 100)
    neutral = 100 - positive - negative
    
    # 生成抑郁风险评估
    depression_risk = assess_depression_risk(text, negative)
    
    # 生成对话记录（模拟）
    dialogue_record = generate_dialogue_record(text)
    
    # 生成评分
    scores = generate_scores(negative, has_negative_words)
    
    # 生成情绪摘要
    emotion_summary = generate_emotion_summary(text, positive, negative, neutral, depression_risk)
    
    return {
        "emotion_values": {
            "positive": positive,
            "negative": negative,
            "neutral": neutral
        },
        "emotion_summary": emotion_summary,
        "depression_risk": depression_risk,
        "dialogue_record": dialogue_record,
        "scores": scores
    }

def assess_depression_risk(text: str, negative_score: int) -> Dict[str, Any]:
    """
    评估抑郁风险
    
    参数:
        text: 用户输入的文本
        negative_score: 负面情绪得分
        
    返回:
        抑郁风险评估结果
    """
    # 检查文本中的关键词
    depression_keywords = ["绝望", "自杀", "没有意义", "活不下去", "痛苦", "抑郁", "失眠", "无法集中注意力"]
    keyword_count = sum(1 for keyword in depression_keywords if keyword in text)
    
    # 基于关键词和负面情绪评分确定风险等级
    if keyword_count >= 3 or negative_score >= 70:
        level = "high"
        text_result = "高风险"
        confidence = random.randint(75, 90)
        description = "您的描述中包含多个抑郁相关指标，建议尽快咨询专业心理医生或精神科医生。"
    elif keyword_count >= 1 or negative_score >= 50:
        level = "medium"
        text_result = "中等风险"
        confidence = random.randint(65, 85)
        description = "您的描述中包含一些抑郁情绪的指标，如持续的疲惫感和睡眠问题。这些可能是压力引起的暂时状态，但也建议您关注自己的情绪变化。如果这些症状持续超过两周，建议咨询专业心理医生。"
    else:
        level = "low"
        text_result = "低风险"
        confidence = random.randint(70, 90)
        description = "根据您的描述，未检测到明显的抑郁风险指标。建议您继续保持健康的生活方式和积极的心态。"
    
    return {
        "level": level,
        "text": text_result,
        "confidence": confidence,
        "description": description
    }

def generate_dialogue_record(text: str) -> List[Dict[str, str]]:
    """
    生成对话记录（模拟）
    
    参数:
        text: 用户输入的文本
        
    返回:
        对话记录列表
    """
    # 初始对话记录包含用户输入
    dialogue = [
        {"speaker": "用户", "content": text}
    ]
    
    # 根据用户输入生成模拟对话
    if "失眠" in text or "睡眠" in text:
        dialogue.append({"speaker": "系统", "content": "您能否描述一下您的睡眠情况？例如，您大约几点入睡，夜间是否会醒来多次？"})
        dialogue.append({"speaker": "用户", "content": "通常晚上11点上床，但要躺1-2小时才能入睡，经常半夜醒来，然后很难再次入睡。"})
    
    if "压力" in text or "焦虑" in text:
        dialogue.append({"speaker": "系统", "content": "了解了。您平时有什么放松或减压的方式吗？"})
        dialogue.append({"speaker": "用户", "content": "没有特别的方式，下班后通常看看手机或电视。"})
    
    # 如果没有特定关键词，添加通用问题
    if len(dialogue) <= 1:
        dialogue.append({"speaker": "系统", "content": "您能详细描述一下这种感受是从什么时候开始的吗？"})
        dialogue.append({"speaker": "用户", "content": "大约从上个月开始，工作变得特别忙，感觉压力很大。"})
    
    # 添加一个后续问题
    dialogue.append({"speaker": "系统", "content": "这种情况对您的日常生活和工作产生了哪些影响？"})
    dialogue.append({"speaker": "用户", "content": "工作效率下降，很难集中注意力，也影响了和家人的关系。"})
    
    return dialogue

def generate_scores(negative_score: int, has_negative_words: bool) -> Dict[str, int]:
    """
    生成各项评分
    
    参数:
        negative_score: 负面情绪得分
        has_negative_words: 是否包含负面词汇
        
    返回:
        各项评分字典
    """
    # 基于负面情绪得分和关键词生成评分
    emotional_health = max(1, 6 - negative_score // 15)
    sleep_quality = 3 if not has_negative_words else 2
    stress_level = min(5, 2 + negative_score // 15)
    overall_wellbeing = max(1, 6 - negative_score // 20)
    
    return {
        "emotionalHealth": emotional_health,
        "sleepQuality": sleep_quality,
        "stressLevel": stress_level,
        "overallWellbeing": overall_wellbeing
    }

def generate_emotion_summary(text: str, positive: int, negative: int, neutral: int, depression_risk: Dict) -> str:
    """
    生成情绪分析摘要
    
    参数:
        text: 用户输入文本
        positive: 积极情绪百分比
        negative: 消极情绪百分比
        neutral: 中性情绪百分比
        depression_risk: 抑郁风险评估结果
        
    返回:
        情绪分析摘要文本
    """
    # 根据情绪分布和风险等级生成摘要
    if negative > 50:
        emotion_state = "较为消极的情绪状态"
    elif negative > 30:
        emotion_state = "轻度压力状态，情绪偏向消极"
    else:
        emotion_state = "情绪状态相对平稳"
    
    # 检测文本中的关键词
    keywords = []
    if "工作" in text or "职场" in text:
        keywords.append("工作压力")
    if "失眠" in text or "睡眠" in text:
        keywords.append("睡眠问题")
    if "疲惫" in text or "累" in text:
        keywords.append("疲惫感")
    if "焦虑" in text:
        keywords.append("焦虑感")
    if "家庭" in text or "家人" in text:
        keywords.append("家庭关系")
    
    # 如果没有检测到关键词，添加一个通用词
    if not keywords:
        keywords.append("情绪波动")
    
    # 生成关键词部分文本
    keywords_text = "、".join(keywords)
    
    # 根据抑郁风险等级添加建议
    if depression_risk["level"] == "high":
        advice = "建议您尽快寻求专业心理咨询或医疗帮助。"
    elif depression_risk["level"] == "medium":
        advice = "建议您关注自己的情绪变化，尝试一些减压方法，必要时寻求专业帮助。"
    else:
        advice = "建议您保持健康的生活方式，定期进行身体活动，保持良好的睡眠习惯。"
    
    # 组合摘要文本
    summary = f"根据您的描述，我们检测到您目前处于{emotion_state}。您的表述中包含了{keywords_text}等指标。{advice}"
    
    return summary

def get_recommendations(emotion_result: Dict[str, Any]) -> List[Dict[str, str]]:
    """
    根据情绪分析结果生成建议
    
    参数:
        emotion_result: 情绪分析结果
        
    返回:
        建议列表
    """
    recommendations = []
    
    # 获取情绪值和抑郁风险
    emotion_values = emotion_result["emotion_values"]
    depression_risk = emotion_result["depression_risk"]
    scores = emotion_result["scores"]
    
    # 睡眠建议
    if scores["sleepQuality"] <= 3:
        recommendations.append({
            "title": "改善睡眠质量",
            "content": "尝试建立规律的睡眠时间表，睡前1小时避免使用电子设备，可以尝试冥想或深呼吸练习来帮助入睡。"
        })
    
    # 身体活动建议
    if emotion_values["negative"] > 40 or scores["stressLevel"] >= 4:
        recommendations.append({
            "title": "身体活动",
            "content": "每天进行30分钟的中等强度运动，如散步、游泳或瑜伽，有助于减轻压力和改善睡眠。"
        })
    
    # 饮食建议
    recommendations.append({
        "title": "饮食建议",
        "content": "减少咖啡因和酒精摄入，增加富含镁的食物（如坚果、绿叶蔬菜）有助于放松神经系统。"
    })
    
    # 工作休息平衡
    if scores["stressLevel"] >= 3:
        recommendations.append({
            "title": "工作休息平衡",
            "content": "工作时每45-60分钟短暂休息5-10分钟，使用番茄工作法可以提高效率并减轻压力。"
        })
    
    # 社交支持
    if depression_risk["level"] in ["medium", "high"]:
        recommendations.append({
            "title": "社交支持",
            "content": "与亲友保持联系，分享您的感受和困扰。社交支持是应对压力和情绪问题的重要资源。"
        })
    
    # 专业帮助
    if depression_risk["level"] == "high":
        recommendations.append({
            "title": "寻求专业帮助",
            "content": "考虑咨询心理医生或精神科医生。专业人士可以提供个性化的治疗方案和支持。"
        })
    
    # 确保至少有4项建议
    if len(recommendations) < 4:
        additional_recommendations = [
            {
                "title": "正念练习",
                "content": "每天花10-15分钟进行正念冥想，关注当下，接受自己的情绪而不评判，有助于减轻焦虑和压力。"
            },
            {
                "title": "限制媒体摄入",
                "content": "减少负面新闻和社交媒体的使用时间，特别是在睡前，可以帮助改善情绪和睡眠质量。"
            },
            {
                "title": "创造性活动",
                "content": "尝试绘画、写作、音乐等创造性活动，这些活动可以作为情绪的出口，帮助您表达和处理复杂的感受。"
            }
        ]
        
        # 添加额外建议直到达到4项
        for rec in additional_recommendations:
            if len(recommendations) >= 4:
                break
            recommendations.append(rec)
    
    return recommendations