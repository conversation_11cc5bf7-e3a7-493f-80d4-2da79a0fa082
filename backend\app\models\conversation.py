from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class MessageType(str, Enum):
    """消息类型枚举"""
    DOCTOR = "doctor"
    PATIENT = "patient"
    SYSTEM = "system"

class MessageFormat(str, Enum):
    """消息格式枚举"""
    TEXT = "text"
    AUDIO = "audio"
    MIXED = "mixed"

class ConversationStatus(str, Enum):
    """对话状态枚举"""
    ACTIVE = "active"
    COMPLETED = "completed"
    PAUSED = "paused"
    TERMINATED = "terminated"

class ConversationMessage(BaseModel):
    """对话消息模型"""
    id: Optional[str] = Field(None, description="消息ID")
    session_id: str = Field(..., description="会话ID")
    message_type: MessageType = Field(..., description="消息类型")
    content: str = Field(..., description="消息内容")
    audio_url: Optional[str] = Field(None, description="音频文件URL")
    format: MessageFormat = Field(default=MessageFormat.TEXT, description="消息格式")
    timestamp: datetime = Field(default_factory=datetime.now, description="消息时间")
    round_number: int = Field(..., ge=1, le=10, description="对话轮次")
    
    # 情绪分析相关
    emotional_tone: Optional[str] = Field(None, description="情绪语调")
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="置信度分数")
    
    # 语音相关
    speech_duration: Optional[float] = Field(None, description="语音时长(秒)")
    speech_speed: Optional[float] = Field(None, description="语速")
    
    # 元数据
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外元数据")

class ConversationSession(BaseModel):
    """对话会话模型"""
    session_id: str = Field(..., description="会话ID")
    patient_id: str = Field(..., description="患者ID")
    doctor_id: Optional[str] = Field(None, description="医生ID")
    
    # 会话状态
    status: ConversationStatus = Field(default=ConversationStatus.ACTIVE, description="会话状态")
    current_round: int = Field(default=0, ge=0, le=10, description="当前轮次")
    max_rounds: int = Field(default=10, description="最大轮次")
    
    # 时间信息
    start_time: datetime = Field(default_factory=datetime.now, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    last_activity: datetime = Field(default_factory=datetime.now, description="最后活动时间")
    
    # 对话消息
    messages: List[ConversationMessage] = Field(default_factory=list, description="对话消息列表")
    
    # 会话配置
    enable_voice: bool = Field(default=True, description="是否启用语音")
    enable_avatar: bool = Field(default=True, description="是否启用数字人")
    language: str = Field(default="zh-CN", description="语言设置")
    
    # 元数据
    metadata: Optional[Dict[str, Any]] = Field(None, description="会话元数据")

class ConversationAnalysis(BaseModel):
    """对话分析模型"""
    session_id: str = Field(..., description="会话ID")
    
    # 分析结果
    emotional_value_score: float = Field(..., ge=0.0, le=100.0, description="情绪价值得分")
    diagnosis_accuracy_score: float = Field(..., ge=0.0, le=100.0, description="诊断正确性得分")
    department_accuracy_score: float = Field(..., ge=0.0, le=100.0, description="科室分配正确性得分")
    overall_score: float = Field(..., ge=0.0, le=100.0, description="总体得分")
    
    # 详细分析
    emotional_analysis: Dict[str, Any] = Field(..., description="情绪分析详情")
    diagnosis_analysis: Dict[str, Any] = Field(..., description="诊断分析详情")
    department_analysis: Dict[str, Any] = Field(..., description="科室分析详情")
    
    # 建议和反馈
    recommendations: List[str] = Field(default_factory=list, description="改进建议")
    strengths: List[str] = Field(default_factory=list, description="优点")
    weaknesses: List[str] = Field(default_factory=list, description="不足")
    
    # 对话记录展示
    conversation_summary: str = Field(..., description="对话摘要")
    key_moments: List[Dict[str, Any]] = Field(default_factory=list, description="关键时刻")
    
    # 分析时间
    analysis_time: datetime = Field(default_factory=datetime.now, description="分析时间")
    
    # 元数据
    metadata: Optional[Dict[str, Any]] = Field(None, description="分析元数据")

class AudioProcessingRequest(BaseModel):
    """音频处理请求模型"""
    audio_data: str = Field(..., description="音频数据(base64编码)")
    format: str = Field(default="wav", description="音频格式")
    language: str = Field(default="zh-CN", description="语言")
    session_id: str = Field(..., description="会话ID")

class AudioProcessingResponse(BaseModel):
    """音频处理响应模型"""
    text: str = Field(..., description="转换后的文本")
    confidence: float = Field(..., ge=0.0, le=1.0, description="识别置信度")
    duration: float = Field(..., description="音频时长")
    processing_time: float = Field(..., description="处理时间")

class TTSRequest(BaseModel):
    """文字转语音请求模型"""
    text: str = Field(..., description="要转换的文本")
    voice_id: Optional[str] = Field(None, description="声音ID")
    speed: float = Field(default=1.0, ge=0.5, le=2.0, description="语速")
    pitch: float = Field(default=1.0, ge=0.5, le=2.0, description="音调")
    language: str = Field(default="zh-CN", description="语言")
    session_id: str = Field(..., description="会话ID")

class TTSResponse(BaseModel):
    """文字转语音响应模型"""
    audio_url: str = Field(..., description="音频文件URL")
    duration: float = Field(..., description="音频时长")
    format: str = Field(..., description="音频格式")
    processing_time: float = Field(..., description="处理时间")

class ConversationRequest(BaseModel):
    """对话请求模型"""
    session_id: str = Field(..., description="会话ID")
    message: str = Field(..., description="医生消息")
    message_type: MessageType = Field(default=MessageType.DOCTOR, description="消息类型")
    audio_data: Optional[str] = Field(None, description="音频数据")
    
class ConversationResponse(BaseModel):
    """对话响应模型"""
    patient_response: str = Field(..., description="患者回复")
    audio_url: Optional[str] = Field(None, description="患者回复音频URL")
    emotional_tone: str = Field(..., description="情绪语调")
    round_number: int = Field(..., description="当前轮次")
    is_conversation_complete: bool = Field(..., description="对话是否完成")
    next_suggestions: Optional[List[str]] = Field(None, description="下一步建议")
