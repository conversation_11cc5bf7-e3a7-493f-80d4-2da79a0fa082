/**
 * Electron主进程
 * 
 * 负责创建和管理应用窗口，处理系统级事件
 */

const { app, BrowserWindow, Menu, dialog, shell, ipcMain, protocol } = require('electron');
const { autoUpdater } = require('electron-updater');
const log = require('electron-log');
const Store = require('electron-store');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

// 配置日志
log.transports.file.level = 'info';
autoUpdater.logger = log;

// 创建配置存储
const store = new Store();

// 全局变量
let mainWindow;
let backendProcess;
let splashWindow;

// 开发模式检测
const isDev = process.argv.includes('--dev') || process.env.NODE_ENV === 'development';

// 单实例锁
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    // 当运行第二个实例时，将焦点放在主窗口上
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}

/**
 * 创建启动画面
 */
function createSplashWindow() {
  splashWindow = new BrowserWindow({
    width: 400,
    height: 300,
    frame: false,
    alwaysOnTop: true,
    transparent: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true
    }
  });

  splashWindow.loadFile(path.join(__dirname, 'splash.html'));
  
  splashWindow.on('closed', () => {
    splashWindow = null;
  });
}

/**
 * 创建主窗口
 */
function createMainWindow() {
  // 获取窗口配置
  const windowConfig = store.get('windowConfig', {
    width: 1200,
    height: 800,
    x: undefined,
    y: undefined
  });

  mainWindow = new BrowserWindow({
    width: windowConfig.width,
    height: windowConfig.height,
    x: windowConfig.x,
    y: windowConfig.y,
    minWidth: 800,
    minHeight: 600,
    show: false, // 初始不显示，等待ready-to-show事件
    icon: path.join(__dirname, 'assets', process.platform === 'win32' ? 'icon.ico' : 'icon.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: !isDev
    },
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default'
  });

  // 加载应用
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, 'dist', 'index.html'));
  }

  // 窗口准备显示时
  mainWindow.once('ready-to-show', () => {
    if (splashWindow) {
      splashWindow.close();
    }
    mainWindow.show();
    
    // 检查更新
    if (!isDev) {
      autoUpdater.checkForUpdatesAndNotify();
    }
  });

  // 窗口关闭前保存配置
  mainWindow.on('close', () => {
    const bounds = mainWindow.getBounds();
    store.set('windowConfig', bounds);
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 处理外部链接
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // 设置菜单
  createMenu();
}

/**
 * 创建应用菜单
 */
function createMenu() {
  const template = [
    {
      label: '文件',
      submenu: [
        {
          label: '新建会话',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-new-session');
          }
        },
        {
          label: '导出PDF',
          accelerator: 'CmdOrCtrl+E',
          click: () => {
            mainWindow.webContents.send('menu-export-pdf');
          }
        },
        { type: 'separator' },
        {
          label: '设置',
          accelerator: 'CmdOrCtrl+,',
          click: () => {
            mainWindow.webContents.send('menu-settings');
          }
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        { role: 'undo', label: '撤销' },
        { role: 'redo', label: '重做' },
        { type: 'separator' },
        { role: 'cut', label: '剪切' },
        { role: 'copy', label: '复制' },
        { role: 'paste', label: '粘贴' },
        { role: 'selectall', label: '全选' }
      ]
    },
    {
      label: '视图',
      submenu: [
        { role: 'reload', label: '重新加载' },
        { role: 'forceReload', label: '强制重新加载' },
        { role: 'toggleDevTools', label: '开发者工具' },
        { type: 'separator' },
        { role: 'resetZoom', label: '实际大小' },
        { role: 'zoomIn', label: '放大' },
        { role: 'zoomOut', label: '缩小' },
        { type: 'separator' },
        { role: 'togglefullscreen', label: '全屏' }
      ]
    },
    {
      label: '窗口',
      submenu: [
        { role: 'minimize', label: '最小化' },
        { role: 'close', label: '关闭' }
      ]
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于虚拟患者对话系统',
              message: '虚拟患者对话系统',
              detail: `版本: ${app.getVersion()}\n基于AI的医学教育虚拟患者对话训练系统\n\n© 2024 Your Company`,
              buttons: ['确定']
            });
          }
        },
        {
          label: '用户手册',
          click: () => {
            shell.openExternal('https://github.com/your-org/virtual-patient-system/wiki');
          }
        },
        {
          label: '报告问题',
          click: () => {
            shell.openExternal('https://github.com/your-org/virtual-patient-system/issues');
          }
        }
      ]
    }
  ];

  // macOS特殊处理
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: 'about', label: '关于 ' + app.getName() },
        { type: 'separator' },
        { role: 'services', label: '服务' },
        { type: 'separator' },
        { role: 'hide', label: '隐藏 ' + app.getName() },
        { role: 'hideothers', label: '隐藏其他' },
        { role: 'unhide', label: '显示全部' },
        { type: 'separator' },
        { role: 'quit', label: '退出 ' + app.getName() }
      ]
    });

    // 窗口菜单
    template[4].submenu = [
      { role: 'close', label: '关闭' },
      { role: 'minimize', label: '最小化' },
      { role: 'zoom', label: '缩放' },
      { type: 'separator' },
      { role: 'front', label: '全部置于顶层' }
    ];
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

/**
 * 启动后端服务
 */
function startBackendService() {
  if (isDev) {
    log.info('开发模式：跳过后端服务启动');
    return;
  }

  const backendPath = path.join(process.resourcesPath, 'backend', 'main.exe');
  
  if (fs.existsSync(backendPath)) {
    try {
      backendProcess = spawn(backendPath, [], {
        cwd: path.dirname(backendPath),
        stdio: 'pipe'
      });

      backendProcess.stdout.on('data', (data) => {
        log.info(`Backend: ${data}`);
      });

      backendProcess.stderr.on('data', (data) => {
        log.error(`Backend Error: ${data}`);
      });

      backendProcess.on('close', (code) => {
        log.info(`Backend process exited with code ${code}`);
      });

      log.info('后端服务启动成功');
    } catch (error) {
      log.error('后端服务启动失败:', error);
    }
  } else {
    log.warn('后端服务文件不存在:', backendPath);
  }
}

/**
 * 停止后端服务
 */
function stopBackendService() {
  if (backendProcess) {
    backendProcess.kill();
    backendProcess = null;
    log.info('后端服务已停止');
  }
}

// IPC事件处理
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('get-app-path', () => {
  return app.getAppPath();
});

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options);
  return result;
});

// 应用事件处理
app.whenReady().then(() => {
  createSplashWindow();
  startBackendService();
  
  setTimeout(() => {
    createMainWindow();
  }, 2000); // 2秒后显示主窗口

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

app.on('window-all-closed', () => {
  stopBackendService();
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', () => {
  stopBackendService();
});

// 自动更新事件
autoUpdater.on('checking-for-update', () => {
  log.info('检查更新中...');
});

autoUpdater.on('update-available', (info) => {
  log.info('发现新版本:', info.version);
});

autoUpdater.on('update-not-available', (info) => {
  log.info('当前已是最新版本');
});

autoUpdater.on('error', (err) => {
  log.error('自动更新错误:', err);
});

autoUpdater.on('download-progress', (progressObj) => {
  let log_message = "下载速度: " + progressObj.bytesPerSecond;
  log_message = log_message + ' - 已下载 ' + progressObj.percent + '%';
  log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')';
  log.info(log_message);
});

autoUpdater.on('update-downloaded', (info) => {
  log.info('更新下载完成');
  autoUpdater.quitAndInstall();
});

// 处理协议
app.setAsDefaultProtocolClient('virtual-patient');

// 安全设置
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});

// 防止导航到外部URL
app.on('web-contents-created', (event, contents) => {
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    if (parsedUrl.origin !== 'http://localhost:3000' && !isDev) {
      event.preventDefault();
    }
  });
});
