# Node.js依赖
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Python依赖
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
pyenv.cfg
.env
.venv
pip-log.txt
pip-delete-this-directory.txt

# 构建输出
/dist
/dist_electron
/build

# 本地配置文件
.env.local
.env.*.local

# 编辑器目录和文件
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store

# 日志文件
logs
*.log

# 数据库文件
*.db
*.sqlite

# 临时文件
.tmp/
.temp/