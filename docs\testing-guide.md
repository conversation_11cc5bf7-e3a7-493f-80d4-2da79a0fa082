# 虚拟患者对话系统测试指南

本文档提供了虚拟患者对话系统的完整测试指南，包括单元测试、集成测试、端到端测试和性能测试。

## 测试架构

```
┌─────────────────────────────────────────────────────────────┐
│                    测试层级架构                              │
├─────────────────────────────────────────────────────────────┤
│  单元测试 (Unit Tests)                                      │
│  ├─ 模型测试 (Pydantic Models)                             │
│  ├─ 服务测试 (Service Layer)                               │
│  ├─ 工具测试 (Utilities)                                   │
│  └─ 组件测试 (Vue Components)                              │
├─────────────────────────────────────────────────────────────┤
│  集成测试 (Integration Tests)                               │
│  ├─ API接口测试                                            │
│  ├─ 数据库集成测试                                          │
│  ├─ AI模型集成测试                                          │
│  └─ 语音服务集成测试                                        │
├─────────────────────────────────────────────────────────────┤
│  端到端测试 (E2E Tests)                                     │
│  ├─ 用户流程测试                                            │
│  ├─ 对话完整流程测试                                        │
│  ├─ 分析报告生成测试                                        │
│  └─ PDF导出测试                                            │
├─────────────────────────────────────────────────────────────┤
│  性能测试 (Performance Tests)                               │
│  ├─ 负载测试                                               │
│  ├─ 压力测试                                               │
│  ├─ AI模型响应时间测试                                      │
│  └─ 并发用户测试                                            │
└─────────────────────────────────────────────────────────────┘
```

## 1. 环境准备

### 1.1 测试环境配置

**requirements-test.txt**:
```
pytest==7.4.0
pytest-asyncio==0.21.0
pytest-mock==3.11.1
httpx==0.24.1
pytest-cov==4.1.0
factory-boy==3.3.0
faker==19.3.0
```

**pytest.ini**:
```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --cov=app
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
asyncio_mode = auto
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
```

### 1.2 测试数据库配置

**conftest.py**:
```python
import pytest
import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from app.services.database import DatabaseService

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
async def test_db():
    """测试数据库连接"""
    client = AsyncIOMotorClient("mongodb://localhost:27017")
    db = client.test_virtual_patient
    
    # 清理测试数据
    await db.drop_collection("virtual_patients")
    await db.drop_collection("conversation_sessions")
    await db.drop_collection("conversation_messages")
    await db.drop_collection("analysis_results")
    
    yield db
    
    # 测试结束后清理
    await client.drop_database("test_virtual_patient")
    client.close()

@pytest.fixture
async def db_service(test_db):
    """数据库服务实例"""
    service = DatabaseService()
    service.database = test_db
    return service
```

## 2. 单元测试

### 2.1 模型测试

**tests/test_models/test_virtual_patient.py**:
```python
import pytest
from app.models.virtual_patient import VirtualPatientPrompt, PatientGender, DepartmentType

class TestVirtualPatientPrompt:
    def test_create_valid_patient(self):
        """测试创建有效患者"""
        patient = VirtualPatientPrompt(
            name="张三",
            age=30,
            gender=PatientGender.MALE,
            chief_complaint="胸痛",
            correct_diagnosis="心肌梗死",
            correct_department=DepartmentType.CARDIOLOGY
        )
        
        assert patient.name == "张三"
        assert patient.age == 30
        assert patient.gender == PatientGender.MALE
    
    def test_age_validation(self):
        """测试年龄验证"""
        with pytest.raises(ValueError):
            VirtualPatientPrompt(
                name="张三",
                age=150,  # 无效年龄
                gender=PatientGender.MALE,
                chief_complaint="胸痛"
            )
    
    def test_patient_serialization(self):
        """测试患者序列化"""
        patient = VirtualPatientPrompt(
            name="李四",
            age=25,
            gender=PatientGender.FEMALE,
            chief_complaint="头痛"
        )
        
        data = patient.dict()
        assert data["name"] == "李四"
        assert data["age"] == 25
        assert data["gender"] == "female"
```

### 2.2 服务测试

**tests/test_services/test_virtual_patient_service.py**:
```python
import pytest
from unittest.mock import AsyncMock, patch
from app.services.virtual_patient_service import virtual_patient_service
from app.models.virtual_patient import VirtualPatientPrompt, PatientGender

class TestVirtualPatientService:
    @pytest.mark.asyncio
    async def test_create_patient_prompt(self, db_service):
        """测试创建患者prompt"""
        patient = VirtualPatientPrompt(
            name="测试患者",
            age=35,
            gender=PatientGender.MALE,
            chief_complaint="测试症状"
        )
        
        with patch.object(virtual_patient_service, '_get_db', return_value=db_service.database):
            created_patient = await virtual_patient_service.create_patient_prompt(patient)
            
            assert created_patient.id is not None
            assert created_patient.name == "测试患者"
            assert created_patient.created_at is not None
    
    @pytest.mark.asyncio
    async def test_get_random_patient_prompt(self, db_service):
        """测试获取随机患者prompt"""
        # 先创建测试数据
        await db_service.database.virtual_patients.insert_one({
            "id": "test-id",
            "name": "测试患者",
            "age": 30,
            "gender": "male",
            "chief_complaint": "测试症状",
            "difficulty_level": 2
        })
        
        with patch.object(virtual_patient_service, '_get_db', return_value=db_service.database):
            patient = await virtual_patient_service.get_random_patient_prompt(difficulty_level=2)
            
            assert patient is not None
            assert patient.name == "测试患者"
```

## 3. 集成测试

### 3.1 API接口测试

**tests/test_api/test_virtual_patient_api.py**:
```python
import pytest
from httpx import AsyncClient
from app.main import app

class TestVirtualPatientAPI:
    @pytest.mark.asyncio
    async def test_generate_patient(self):
        """测试生成虚拟患者API"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post("/api/virtual-patient/generate", json={
                "difficulty_level": 2,
                "department_preference": "cardiology"
            })
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "patient" in data
            assert "session_id" in data
    
    @pytest.mark.asyncio
    async def test_get_patient_prompts(self):
        """测试获取患者prompt列表API"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/api/virtual-patient/prompts")
            
            assert response.status_code == 200
            data = response.json()
            assert isinstance(data, list)
```

### 3.2 对话流程测试

**tests/test_integration/test_conversation_flow.py**:
```python
import pytest
from httpx import AsyncClient
from app.main import app

class TestConversationFlow:
    @pytest.mark.asyncio
    async def test_complete_conversation_flow(self):
        """测试完整对话流程"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # 1. 生成患者
            gen_response = await client.post("/api/virtual-patient/generate", json={
                "difficulty_level": 1
            })
            assert gen_response.status_code == 200
            gen_data = gen_response.json()
            session_id = gen_data["session_id"]
            patient_id = gen_data["patient"]["id"]
            
            # 2. 开始对话
            start_response = await client.post("/api/conversation/start", json={
                "session_id": session_id,
                "patient_id": patient_id
            })
            assert start_response.status_code == 200
            
            # 3. 发送消息
            msg_response = await client.post("/api/conversation/message", json={
                "session_id": session_id,
                "message": "您好，请问您哪里不舒服？",
                "message_type": "text"
            })
            assert msg_response.status_code == 200
            msg_data = msg_response.json()
            assert "patient_response" in msg_data
            
            # 4. 触发分析
            analysis_response = await client.post(f"/api/analysis/trigger/{session_id}")
            assert analysis_response.status_code == 200
            analysis_data = analysis_response.json()
            assert analysis_data["success"] is True
```

## 4. 端到端测试

### 4.1 前端E2E测试

**tests/e2e/test_user_journey.py** (使用Playwright):
```python
import pytest
from playwright.async_api import async_playwright

class TestUserJourney:
    @pytest.mark.asyncio
    async def test_complete_user_journey(self):
        """测试完整用户旅程"""
        async with async_playwright() as p:
            browser = await p.chromium.launch()
            page = await browser.new_page()
            
            # 1. 访问首页
            await page.goto("http://localhost:3000")
            await page.wait_for_selector(".start-btn")
            
            # 2. 配置并开始对话
            await page.select_option("select[name='difficulty']", "2")
            await page.click(".start-btn")
            
            # 3. 等待对话页面加载
            await page.wait_for_selector(".chat-input-area")
            
            # 4. 发送消息
            await page.fill(".message-input", "您好，请问您哪里不舒服？")
            await page.click(".send-btn")
            
            # 5. 等待患者回复
            await page.wait_for_selector(".patient-message", timeout=10000)
            
            # 6. 提交分析
            await page.click("button:has-text('提交分析')")
            
            # 7. 查看分析结果
            await page.wait_for_selector(".analysis-result-container")
            
            await browser.close()
```

## 5. 性能测试

### 5.1 负载测试

**tests/performance/test_load.py**:
```python
import asyncio
import time
import statistics
from httpx import AsyncClient
from app.main import app

class TestPerformance:
    @pytest.mark.asyncio
    async def test_concurrent_conversations(self):
        """测试并发对话性能"""
        async def single_conversation():
            async with AsyncClient(app=app, base_url="http://test") as client:
                start_time = time.time()
                
                # 生成患者
                response = await client.post("/api/virtual-patient/generate", json={
                    "difficulty_level": 1
                })
                
                end_time = time.time()
                return end_time - start_time
        
        # 并发执行10个对话
        tasks = [single_conversation() for _ in range(10)]
        response_times = await asyncio.gather(*tasks)
        
        # 性能断言
        avg_time = statistics.mean(response_times)
        max_time = max(response_times)
        
        assert avg_time < 2.0, f"平均响应时间过长: {avg_time:.2f}s"
        assert max_time < 5.0, f"最大响应时间过长: {max_time:.2f}s"
        
        print(f"平均响应时间: {avg_time:.2f}s")
        print(f"最大响应时间: {max_time:.2f}s")
```

## 6. AI模型测试

### 6.1 模型集成测试

**tests/test_ai/test_model_integration.py**:
```python
import pytest
from unittest.mock import AsyncMock, patch
from app.services.ai_model_manager import ai_model_manager

class TestAIModelIntegration:
    @pytest.mark.asyncio
    async def test_avatar_generation_mock(self):
        """测试数字人生成（模拟）"""
        mock_response = {
            "avatar_url": "/static/avatars/test.png",
            "image_id": "test-id"
        }
        
        with patch.object(ai_model_manager, 'call_service', return_value=mock_response):
            result = await ai_model_manager.generate_avatar({
                "name": "测试患者",
                "age": 30,
                "gender": "male"
            })
            
            assert result["avatar_url"] == "/static/avatars/test.png"
    
    @pytest.mark.asyncio
    async def test_conversation_ai_mock(self):
        """测试对话AI（模拟）"""
        mock_response = {
            "patient_response": "我感觉胸部有些疼痛。",
            "tokens_used": 50
        }
        
        with patch.object(ai_model_manager, 'call_service', return_value=mock_response):
            result = await ai_model_manager.get_patient_response(
                session_id="test-session",
                doctor_message="您好，请问您哪里不舒服？",
                conversation_context=[],
                patient_prompt="测试prompt"
            )
            
            assert result["patient_response"] == "我感觉胸部有些疼痛。"
```

## 7. 测试执行

### 7.1 运行所有测试
```bash
# 运行所有测试
pytest

# 运行特定类型的测试
pytest -m unit
pytest -m integration
pytest -m e2e

# 运行覆盖率测试
pytest --cov=app --cov-report=html

# 运行性能测试
pytest -m slow
```

### 7.2 持续集成配置

**.github/workflows/test.yml**:
```yaml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:5.0
        ports:
          - 27017:27017
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-test.txt
    
    - name: Run tests
      run: |
        pytest -v --cov=app --cov-report=xml
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
```

## 8. 测试最佳实践

### 8.1 测试原则
1. **独立性**: 每个测试应该独立运行
2. **可重复性**: 测试结果应该一致
3. **快速性**: 单元测试应该快速执行
4. **清晰性**: 测试代码应该易于理解

### 8.2 Mock策略
- 对外部API调用使用Mock
- 对AI模型调用使用Mock（除非专门测试模型）
- 对数据库操作使用测试数据库

### 8.3 测试数据管理
- 使用Factory模式生成测试数据
- 每个测试后清理数据
- 使用固定的测试数据集

通过以上测试策略，可以确保虚拟患者对话系统的质量和稳定性。
