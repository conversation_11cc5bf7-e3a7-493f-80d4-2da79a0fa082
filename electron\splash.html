<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚拟患者对话系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            overflow: hidden;
            color: white;
        }

        .splash-container {
            text-align: center;
            animation: fadeIn 0.8s ease-in-out;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            animation: pulse 2s infinite;
        }

        .app-name {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            letter-spacing: 1px;
        }

        .app-description {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 30px;
        }

        .loading-container {
            position: relative;
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            margin: 0 auto;
            overflow: hidden;
        }

        .loading-bar {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 2px;
            animation: loading 2s ease-in-out infinite;
        }

        .loading-text {
            margin-top: 15px;
            font-size: 12px;
            opacity: 0.7;
            animation: blink 1.5s infinite;
        }

        .version {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 11px;
            opacity: 0.5;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes loading {
            0% {
                width: 0%;
                margin-left: 0%;
            }
            50% {
                width: 75%;
                margin-left: 12.5%;
            }
            100% {
                width: 0%;
                margin-left: 100%;
            }
        }

        @keyframes blink {
            0%, 50% {
                opacity: 0.7;
            }
            51%, 100% {
                opacity: 0.3;
            }
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .app-name {
                font-size: 20px;
            }
            
            .app-description {
                font-size: 12px;
            }
            
            .logo {
                width: 60px;
                height: 60px;
                font-size: 28px;
            }
        }

        /* 深色主题适配 */
        @media (prefers-color-scheme: dark) {
            body {
                background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            }
        }

        /* 高对比度模式 */
        @media (prefers-contrast: high) {
            body {
                background: #000;
                color: #fff;
            }
            
            .logo {
                background: rgba(255, 255, 255, 0.8);
                color: #000;
            }
            
            .loading-container {
                background: rgba(255, 255, 255, 0.8);
            }
        }

        /* 减少动画模式 */
        @media (prefers-reduced-motion: reduce) {
            .logo,
            .loading-bar,
            .loading-text {
                animation: none;
            }
            
            .splash-container {
                animation: none;
            }
        }
    </style>
</head>
<body>
    <div class="splash-container">
        <div class="logo">
            🏥
        </div>
        <div class="app-name">虚拟患者对话系统</div>
        <div class="app-description">基于AI的医学教育训练平台</div>
        
        <div class="loading-container">
            <div class="loading-bar"></div>
        </div>
        <div class="loading-text">正在启动应用...</div>
    </div>
    
    <div class="version">v1.0.0</div>

    <script>
        // 启动画面逻辑
        const loadingTexts = [
            '正在启动应用...',
            '正在加载组件...',
            '正在初始化服务...',
            '准备就绪...'
        ];
        
        let currentTextIndex = 0;
        const loadingTextElement = document.querySelector('.loading-text');
        
        // 更新加载文本
        function updateLoadingText() {
            if (currentTextIndex < loadingTexts.length) {
                loadingTextElement.textContent = loadingTexts[currentTextIndex];
                currentTextIndex++;
                setTimeout(updateLoadingText, 500);
            }
        }
        
        // 开始更新文本
        setTimeout(updateLoadingText, 500);
        
        // 获取应用版本
        if (window.electronAPI) {
            window.electronAPI.getAppVersion().then(version => {
                document.querySelector('.version').textContent = `v${version}`;
            }).catch(() => {
                // 如果获取版本失败，保持默认版本
            });
        }
        
        // 键盘事件处理
        document.addEventListener('keydown', (event) => {
            // ESC键关闭启动画面（开发模式）
            if (event.key === 'Escape') {
                window.close();
            }
        });
        
        // 点击事件处理
        document.addEventListener('click', () => {
            // 点击任意位置关闭启动画面（开发模式）
            if (process.env.NODE_ENV === 'development') {
                window.close();
            }
        });
        
        // 自动关闭启动画面
        setTimeout(() => {
            // 启动画面显示3秒后自动关闭
            if (window.close) {
                window.close();
            }
        }, 3000);
        
        // 错误处理
        window.addEventListener('error', (event) => {
            console.error('启动画面错误:', event.error);
        });
        
        // 页面加载完成
        window.addEventListener('load', () => {
            console.log('启动画面加载完成');
        });
    </script>
</body>
</html>
