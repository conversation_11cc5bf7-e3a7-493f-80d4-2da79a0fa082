{"name": "virtual-patient-system", "version": "1.0.0", "description": "虚拟患者对话系统桌面版", "main": "main.js", "author": "Your Company", "license": "MIT", "homepage": "https://github.com/your-org/virtual-patient-system", "repository": {"type": "git", "url": "https://github.com/your-org/virtual-patient-system.git"}, "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-linux": "electron-builder --linux", "build-mac": "electron-builder --mac", "pack": "electron-builder --dir", "dist": "npm run build", "rebuild": "electron-rebuild", "postinstall": "electron-builder install-app-deps", "clean": "<PERSON><PERSON><PERSON> dist build", "test": "jest"}, "build": {"appId": "com.yourcompany.virtual-patient", "productName": "虚拟患者对话系统", "copyright": "Copyright © 2024 Your Company", "directories": {"output": "dist", "buildResources": "build"}, "files": ["main.js", "preload.js", "renderer.js", "package.json", "dist/**/*", "assets/**/*", "node_modules/**/*", "!node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!node_modules/*.d.ts", "!node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "extraResources": [{"from": "../backend/dist", "to": "backend", "filter": ["**/*"]}, {"from": "../docs", "to": "docs", "filter": ["*.md", "*.pdf"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}", "publisherName": "Your Company"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Education", "artifactName": "${productName}-${version}-${arch}.${ext}", "desktop": {"Name": "虚拟患者对话系统", "Comment": "医学教育虚拟患者对话训练系统", "Keywords": "medical;education;training;patient;conversation;", "StartupWMClass": "virtual-patient-system"}}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.education", "artifactName": "${productName}-${version}-${arch}.${ext}", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "installerHeaderIcon": "assets/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "虚拟患者对话系统", "include": "build/installer.nsh", "script": "build/installer.nsh", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Education"}, "dmg": {"title": "${productName} ${version}", "icon": "assets/icon.icns", "iconSize": 100, "contents": [{"x": 380, "y": 280, "type": "link", "path": "/Applications"}, {"x": 110, "y": 280, "type": "file"}], "window": {"width": 540, "height": 380}}, "portable": {"artifactName": "${productName}-${version}-portable.${ext}"}, "appImage": {"artifactName": "${productName}-${version}.${ext}"}, "deb": {"depends": ["gconf2", "gconf-service", "libnotify4", "libappindicator1", "libxtst6", "libnss3"], "artifactName": "${productName}-${version}.${ext}"}, "rpm": {"depends": ["libXScrnSaver"], "artifactName": "${productName}-${version}.${ext}"}, "publish": {"provider": "github", "owner": "your-org", "repo": "virtual-patient-system", "private": false, "releaseType": "release"}}, "dependencies": {"electron-updater": "^6.1.4", "electron-log": "^4.4.8", "electron-store": "^8.1.0", "node-fetch": "^3.3.2", "ws": "^8.14.2"}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4", "electron-rebuild": "^3.2.9", "rimraf": "^5.0.5", "jest": "^29.7.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}