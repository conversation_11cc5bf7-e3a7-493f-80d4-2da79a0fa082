"""
AI模型管理器

统一管理所有AI模型的调用，包括：
1. 模型服务健康检查
2. 负载均衡和故障转移
3. 缓存管理
4. 性能监控
"""

import logging
import asyncio
import json
import time
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import httpx
import hashlib

logger = logging.getLogger(__name__)

class ModelServiceConfig:
    """模型服务配置"""
    def __init__(self, name: str, url: str, timeout: float = 30.0, max_retries: int = 3):
        self.name = name
        self.url = url
        self.timeout = timeout
        self.max_retries = max_retries
        self.is_healthy = True
        self.last_health_check = None
        self.response_times = []
        self.error_count = 0

class AIModelManager:
    """AI模型管理器"""
    
    def __init__(self):
        self.services = {}
        self.cache = {}  # 简单内存缓存
        self.cache_ttl = 3600  # 缓存1小时
        
        # 初始化服务配置
        self._init_services()
        
        # 启动健康检查任务
        self.health_check_task = None
        
    def _init_services(self):
        """初始化AI服务配置"""
        self.services = {
            "avatar_generator": ModelServiceConfig(
                "数字人生成", 
                "http://localhost:8001",
                timeout=60.0
            ),
            "conversation_ai": ModelServiceConfig(
                "对话AI", 
                "http://localhost:8002",
                timeout=30.0
            ),
            "analysis_ai": ModelServiceConfig(
                "分析AI", 
                "http://localhost:8003",
                timeout=45.0
            ),
            "diagnosis_ai": ModelServiceConfig(
                "诊断AI", 
                "http://localhost:8004",
                timeout=30.0
            )
        }
    
    async def start_health_monitoring(self):
        """启动健康监控"""
        if self.health_check_task is None:
            self.health_check_task = asyncio.create_task(self._health_check_loop())
    
    async def stop_health_monitoring(self):
        """停止健康监控"""
        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
            self.health_check_task = None
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await self._check_all_services_health()
                await asyncio.sleep(60)  # 每分钟检查一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查异常: {str(e)}")
                await asyncio.sleep(60)
    
    async def _check_all_services_health(self):
        """检查所有服务健康状态"""
        tasks = []
        for service_name, config in self.services.items():
            task = asyncio.create_task(self._check_service_health(service_name, config))
            tasks.append(task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _check_service_health(self, service_name: str, config: ModelServiceConfig):
        """检查单个服务健康状态"""
        try:
            start_time = time.time()
            
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{config.url}/health")
                
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                config.is_healthy = True
                config.error_count = 0
                config.response_times.append(response_time)
                
                # 只保留最近10次响应时间
                if len(config.response_times) > 10:
                    config.response_times = config.response_times[-10:]
                    
                logger.debug(f"服务 {service_name} 健康检查通过，响应时间: {response_time:.2f}s")
            else:
                config.is_healthy = False
                config.error_count += 1
                logger.warning(f"服务 {service_name} 健康检查失败: {response.status_code}")
                
        except Exception as e:
            config.is_healthy = False
            config.error_count += 1
            logger.error(f"服务 {service_name} 健康检查异常: {str(e)}")
        
        config.last_health_check = datetime.now()
    
    def _get_cache_key(self, service_name: str, request_data: Dict[str, Any]) -> str:
        """生成缓存键"""
        # 将请求数据序列化并生成hash
        data_str = json.dumps(request_data, sort_keys=True, ensure_ascii=False)
        hash_obj = hashlib.md5(data_str.encode('utf-8'))
        return f"{service_name}:{hash_obj.hexdigest()}"
    
    def _get_from_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """从缓存获取数据"""
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if datetime.now() - timestamp < timedelta(seconds=self.cache_ttl):
                return cached_data
            else:
                # 缓存过期，删除
                del self.cache[cache_key]
        return None
    
    def _set_cache(self, cache_key: str, data: Dict[str, Any]):
        """设置缓存数据"""
        self.cache[cache_key] = (data, datetime.now())
        
        # 简单的缓存清理：如果缓存过多，删除最旧的
        if len(self.cache) > 1000:
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k][1])
            del self.cache[oldest_key]
    
    async def call_service(
        self, 
        service_name: str, 
        endpoint: str, 
        request_data: Dict[str, Any],
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        调用AI服务
        
        Args:
            service_name: 服务名称
            endpoint: API端点
            request_data: 请求数据
            use_cache: 是否使用缓存
            
        Returns:
            Dict: 服务响应数据
        """
        if service_name not in self.services:
            raise ValueError(f"未知的服务: {service_name}")
        
        config = self.services[service_name]
        
        # 检查服务健康状态
        if not config.is_healthy:
            raise Exception(f"服务 {service_name} 当前不可用")
        
        # 检查缓存
        cache_key = None
        if use_cache:
            cache_key = self._get_cache_key(service_name, request_data)
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                logger.debug(f"从缓存返回 {service_name} 结果")
                return cached_result
        
        # 调用服务
        url = f"{config.url}{endpoint}"
        
        for attempt in range(config.max_retries):
            try:
                start_time = time.time()
                
                async with httpx.AsyncClient(timeout=config.timeout) as client:
                    response = await client.post(url, json=request_data)
                
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # 记录性能指标
                    config.response_times.append(response_time)
                    if len(config.response_times) > 10:
                        config.response_times = config.response_times[-10:]
                    
                    # 缓存结果
                    if use_cache and cache_key:
                        self._set_cache(cache_key, result)
                    
                    logger.info(f"服务 {service_name} 调用成功，响应时间: {response_time:.2f}s")
                    return result
                else:
                    logger.error(f"服务 {service_name} 返回错误: {response.status_code} - {response.text}")
                    if attempt == config.max_retries - 1:
                        raise Exception(f"服务调用失败: {response.status_code}")
                    
            except httpx.TimeoutException:
                logger.warning(f"服务 {service_name} 调用超时 (尝试 {attempt + 1}/{config.max_retries})")
                if attempt == config.max_retries - 1:
                    config.is_healthy = False
                    raise Exception("服务调用超时")
                    
            except Exception as e:
                logger.error(f"服务 {service_name} 调用异常: {str(e)} (尝试 {attempt + 1}/{config.max_retries})")
                if attempt == config.max_retries - 1:
                    config.is_healthy = False
                    raise
                
                # 等待后重试
                await asyncio.sleep(1 * (attempt + 1))
    
    async def generate_avatar(self, patient_info: Dict[str, Any]) -> Dict[str, Any]:
        """生成数字人头像"""
        request_data = {
            "patient_info": patient_info,
            "style": "realistic",
            "quality": "high"
        }
        
        return await self.call_service(
            "avatar_generator", 
            "/generate-avatar", 
            request_data,
            use_cache=True
        )
    
    async def get_patient_response(
        self, 
        session_id: str,
        doctor_message: str,
        conversation_context: List[Dict[str, str]],
        patient_prompt: str
    ) -> Dict[str, Any]:
        """获取患者回复"""
        request_data = {
            "session_id": session_id,
            "doctor_message": doctor_message,
            "conversation_context": conversation_context,
            "patient_prompt": patient_prompt,
            "response_style": "patient_roleplay"
        }
        
        return await self.call_service(
            "conversation_ai",
            "/patient-response",
            request_data,
            use_cache=False  # 对话不使用缓存
        )
    
    async def analyze_conversation(
        self,
        session_id: str,
        conversation_data: Dict[str, Any],
        patient_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """分析对话"""
        request_data = {
            "session_id": session_id,
            "conversation_data": conversation_data,
            "patient_info": patient_info,
            "analysis_dimensions": [
                "emotional_value",
                "diagnosis_accuracy", 
                "department_accuracy",
                "conversation_quality"
            ]
        }
        
        return await self.call_service(
            "analysis_ai",
            "/analyze-conversation",
            request_data,
            use_cache=True
        )
    
    async def evaluate_diagnosis(
        self,
        doctor_diagnosis: str,
        correct_diagnosis: str,
        conversation_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """评估诊断"""
        request_data = {
            "doctor_diagnosis": doctor_diagnosis,
            "correct_diagnosis": correct_diagnosis,
            "conversation_data": conversation_data
        }
        
        return await self.call_service(
            "diagnosis_ai",
            "/evaluate-diagnosis",
            request_data,
            use_cache=True
        )
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取所有服务状态"""
        status = {}
        for service_name, config in self.services.items():
            avg_response_time = 0
            if config.response_times:
                avg_response_time = sum(config.response_times) / len(config.response_times)
            
            status[service_name] = {
                "name": config.name,
                "url": config.url,
                "is_healthy": config.is_healthy,
                "last_health_check": config.last_health_check.isoformat() if config.last_health_check else None,
                "error_count": config.error_count,
                "avg_response_time": round(avg_response_time, 3),
                "recent_response_times": config.response_times[-5:]  # 最近5次响应时间
            }
        
        return status

# 创建全局AI模型管理器实例
ai_model_manager = AIModelManager()
