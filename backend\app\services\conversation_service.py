from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime
import logging

from app.models.conversation import (
    ConversationSession, ConversationMessage, MessageType, 
    ConversationStatus, ConversationRequest, ConversationResponse,
    AudioProcessingRequest, AudioProcessingResponse,
    TTSRequest, TTSResponse
)
from app.services.database import get_database

logger = logging.getLogger(__name__)

class ConversationService:
    """对话服务类"""
    
    def __init__(self):
        self.db = None
    
    async def _get_db(self):
        """获取数据库连接"""
        if not self.db:
            db_service = await get_database()
            self.db = db_service.database
        return self.db
    
    async def create_session(self, patient_id: str, doctor_id: Optional[str] = None) -> ConversationSession:
        """创建对话会话"""
        try:
            session_id = str(uuid.uuid4())
            session = ConversationSession(
                session_id=session_id,
                patient_id=patient_id,
                doctor_id=doctor_id
            )
            
            db = await self._get_db()
            await db.conversation_sessions.insert_one(session.dict())
            
            logger.info(f"创建对话会话成功: {session_id}")
            return session
            
        except Exception as e:
            logger.error(f"创建对话会话失败: {str(e)}")
            raise
    
    async def get_session(self, session_id: str) -> Optional[ConversationSession]:
        """获取对话会话"""
        try:
            db = await self._get_db()
            session_data = await db.conversation_sessions.find_one({"session_id": session_id})
            
            if session_data:
                session_data.pop('_id', None)
                return ConversationSession(**session_data)
            return None
            
        except Exception as e:
            logger.error(f"获取对话会话失败: {str(e)}")
            raise
    
    async def add_message(self, session_id: str, message_type: MessageType, 
                         content: str, round_number: int, 
                         audio_url: Optional[str] = None) -> ConversationMessage:
        """添加对话消息"""
        try:
            message_id = str(uuid.uuid4())
            message = ConversationMessage(
                id=message_id,
                session_id=session_id,
                message_type=message_type,
                content=content,
                round_number=round_number,
                audio_url=audio_url
            )
            
            db = await self._get_db()
            await db.conversation_messages.insert_one(message.dict())
            
            # 更新会话的最后活动时间
            await db.conversation_sessions.update_one(
                {"session_id": session_id},
                {
                    "$set": {"last_activity": datetime.now()},
                    "$push": {"messages": message.dict()}
                }
            )
            
            logger.info(f"添加对话消息成功: {message_id}")
            return message
            
        except Exception as e:
            logger.error(f"添加对话消息失败: {str(e)}")
            raise
    
    async def process_conversation(self, request: ConversationRequest) -> ConversationResponse:
        """处理对话请求"""
        try:
            # 获取会话信息
            session = await self.get_session(request.session_id)
            if not session:
                raise Exception(f"会话不存在: {request.session_id}")
            
            # 检查对话是否已完成
            if session.current_round >= session.max_rounds:
                raise Exception("对话已达到最大轮次")
            
            # 更新当前轮次
            current_round = session.current_round + 1
            
            # 添加医生消息
            await self.add_message(
                session_id=request.session_id,
                message_type=request.message_type,
                content=request.message,
                round_number=current_round
            )
            
            # TODO: 调用AI模型生成患者回复
            patient_response = await self._generate_patient_response(
                session_id=request.session_id,
                doctor_message=request.message,
                round_number=current_round
            )
            
            # 添加患者回复消息
            await self.add_message(
                session_id=request.session_id,
                message_type=MessageType.PATIENT,
                content=patient_response["text"],
                round_number=current_round,
                audio_url=patient_response.get("audio_url")
            )
            
            # 更新会话状态
            is_complete = current_round >= session.max_rounds
            new_status = ConversationStatus.COMPLETED if is_complete else ConversationStatus.ACTIVE
            
            db = await self._get_db()
            await db.conversation_sessions.update_one(
                {"session_id": request.session_id},
                {
                    "$set": {
                        "current_round": current_round,
                        "status": new_status,
                        "last_activity": datetime.now()
                    }
                }
            )
            
            if is_complete:
                await db.conversation_sessions.update_one(
                    {"session_id": request.session_id},
                    {"$set": {"end_time": datetime.now()}}
                )
            
            return ConversationResponse(
                patient_response=patient_response["text"],
                audio_url=patient_response.get("audio_url"),
                emotional_tone=patient_response.get("emotional_tone", "neutral"),
                round_number=current_round,
                is_conversation_complete=is_complete,
                next_suggestions=patient_response.get("suggestions")
            )
            
        except Exception as e:
            logger.error(f"处理对话请求失败: {str(e)}")
            raise
    
    async def _generate_patient_response(self, session_id: str, doctor_message: str, 
                                       round_number: int) -> Dict[str, Any]:
        """生成患者回复（AI模型接口预留）"""
        # TODO: 这里应该调用AI模型生成患者回复
        # 目前返回模拟数据
        
        # 模拟不同轮次的回复
        mock_responses = {
            1: "医生您好，我最近感觉不太舒服，想来看看。",
            2: "主要是胸闷气短，特别是爬楼梯的时候更明显。",
            3: "大概有一个月了吧，刚开始以为是累的，但是休息了也没有好转。",
            4: "有时候还会心慌，特别是晚上的时候，我很担心是不是心脏有问题。",
            5: "我妈妈有冠心病，所以我特别担心会不会遗传。",
            6: "睡眠不太好，经常因为担心这个问题而失眠。",
            7: "平时工作压力不大，我已经退休了，主要是在家带孙子。",
            8: "血压有点高，在吃降压药，其他没有什么大病。",
            9: "希望医生能帮我检查一下，看看到底是什么问题。",
            10: "谢谢医生，我会按照您的建议去做的。"
        }
        
        response_text = mock_responses.get(round_number, "我明白了，谢谢医生的建议。")
        
        return {
            "text": response_text,
            "audio_url": None,  # TODO: 调用TTS生成音频
            "emotional_tone": "worried" if round_number <= 5 else "relieved",
            "suggestions": ["询问具体症状", "了解病史", "给出建议"] if round_number < 10 else None
        }
    
    async def process_audio_to_text(self, request: AudioProcessingRequest) -> AudioProcessingResponse:
        """音频转文字（STT接口预留）"""
        # TODO: 调用Whisper或其他STT服务
        return AudioProcessingResponse(
            text="这是模拟的语音识别结果",
            confidence=0.95,
            duration=3.5,
            processing_time=0.8
        )
    
    async def process_text_to_speech(self, request: TTSRequest) -> TTSResponse:
        """文字转语音（TTS接口预留）"""
        # TODO: 调用TTS服务生成语音
        return TTSResponse(
            audio_url="/api/audio/mock_audio.wav",
            duration=len(request.text) * 0.1,  # 模拟时长
            format="wav",
            processing_time=1.2
        )
    
    async def get_conversation_history(self, session_id: str) -> List[ConversationMessage]:
        """获取对话历史"""
        try:
            db = await self._get_db()
            messages_cursor = db.conversation_messages.find(
                {"session_id": session_id}
            ).sort("timestamp", 1)
            
            messages_data = await messages_cursor.to_list(length=None)
            messages = []
            
            for msg_data in messages_data:
                msg_data.pop('_id', None)
                messages.append(ConversationMessage(**msg_data))
            
            return messages
            
        except Exception as e:
            logger.error(f"获取对话历史失败: {str(e)}")
            raise

# 全局服务实例
conversation_service = ConversationService()
