#!/bin/bash

echo "正在启动情绪分析助手..."

# 检查是否安装了Node.js
if ! command -v node &> /dev/null; then
    echo "错误: 未找到Node.js，请安装Node.js后再试。"
    exit 1
fi

# 检查是否安装了Python
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python，请安装Python后再试。"
    exit 1
fi

# 检查前端依赖
if [ ! -d "frontend/node_modules" ]; then
    echo "正在安装前端依赖..."
    cd frontend
    npm install
    if [ $? -ne 0 ]; then
        echo "错误: 前端依赖安装失败。"
        cd ..
        exit 1
    fi
    cd ..
fi

# 检查后端依赖
echo "正在检查后端依赖..."
cd backend
python3 -m pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "错误: 后端依赖安装失败。"
    cd ..
    exit 1
fi
cd ..

# 启动应用
echo "正在启动应用..."
cd frontend
npm run electron:serve &

echo "应用启动成功！"