from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class PatientGender(str, Enum):
    """患者性别枚举"""
    MALE = "male"
    FEMALE = "female"
    OTHER = "other"

class PatientAgeGroup(str, Enum):
    """患者年龄组枚举"""
    CHILD = "child"  # 0-12岁
    TEENAGER = "teenager"  # 13-17岁
    YOUNG_ADULT = "young_adult"  # 18-35岁
    MIDDLE_AGED = "middle_aged"  # 36-55岁
    ELDERLY = "elderly"  # 56岁以上

class DepartmentType(str, Enum):
    """科室类型枚举"""
    INTERNAL_MEDICINE = "internal_medicine"  # 内科
    SURGERY = "surgery"  # 外科
    PEDIATRICS = "pediatrics"  # 儿科
    GYNECOLOGY = "gynecology"  # 妇科
    ORTHOPEDICS = "orthopedics"  # 骨科
    CARDIOLOGY = "cardiology"  # 心内科
    NEUROLOGY = "neurology"  # 神经科
    PSYCHIATRY = "psychiatry"  # 精神科
    DERMATOLOGY = "dermatology"  # 皮肤科
    OPHTHALMOLOGY = "ophthalmology"  # 眼科
    ENT = "ent"  # 耳鼻喉科
    EMERGENCY = "emergency"  # 急诊科

class SeverityLevel(str, Enum):
    """病情严重程度枚举"""
    MILD = "mild"  # 轻度
    MODERATE = "moderate"  # 中度
    SEVERE = "severe"  # 重度
    CRITICAL = "critical"  # 危重

class VirtualPatientPrompt(BaseModel):
    """虚拟患者Prompt模型"""
    id: Optional[str] = Field(None, description="患者ID")
    name: str = Field(..., description="患者姓名")
    age: int = Field(..., ge=0, le=120, description="患者年龄")
    gender: PatientGender = Field(..., description="患者性别")
    age_group: PatientAgeGroup = Field(..., description="年龄组")
    
    # 基本信息
    occupation: Optional[str] = Field(None, description="职业")
    education_level: Optional[str] = Field(None, description="教育水平")
    marital_status: Optional[str] = Field(None, description="婚姻状况")
    
    # 病情信息
    chief_complaint: str = Field(..., description="主诉")
    present_illness: str = Field(..., description="现病史")
    past_medical_history: Optional[str] = Field(None, description="既往病史")
    family_history: Optional[str] = Field(None, description="家族史")
    
    # 症状描述
    symptoms: List[str] = Field(default_factory=list, description="症状列表")
    symptom_duration: Optional[str] = Field(None, description="症状持续时间")
    severity: SeverityLevel = Field(..., description="病情严重程度")
    
    # 正确诊断和科室
    correct_diagnosis: str = Field(..., description="正确诊断")
    correct_department: DepartmentType = Field(..., description="正确科室")
    
    # 对话风格和性格
    personality_traits: List[str] = Field(default_factory=list, description="性格特征")
    communication_style: str = Field(..., description="沟通风格")
    emotional_state: str = Field(..., description="情绪状态")
    cooperation_level: str = Field(..., description="配合程度")
    
    # AI对话提示词
    system_prompt: str = Field(..., description="系统提示词")
    conversation_guidelines: List[str] = Field(default_factory=list, description="对话指导原则")
    
    # 数字人相关
    avatar_description: Optional[str] = Field(None, description="数字人外观描述")
    voice_characteristics: Optional[str] = Field(None, description="声音特征")
    
    # 元数据
    created_at: Optional[datetime] = Field(default_factory=datetime.now, description="创建时间")
    updated_at: Optional[datetime] = Field(default_factory=datetime.now, description="更新时间")
    difficulty_level: int = Field(default=1, ge=1, le=5, description="难度等级")
    tags: List[str] = Field(default_factory=list, description="标签")

class VirtualPatientResponse(BaseModel):
    """虚拟患者响应模型"""
    patient_id: str = Field(..., description="患者ID")
    response_text: str = Field(..., description="响应文本")
    emotional_tone: str = Field(..., description="情绪语调")
    response_type: str = Field(..., description="响应类型")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")

class PatientGenerationRequest(BaseModel):
    """患者生成请求模型"""
    difficulty_level: Optional[int] = Field(None, ge=1, le=5, description="难度等级")
    department_preference: Optional[DepartmentType] = Field(None, description="科室偏好")
    age_group_preference: Optional[PatientAgeGroup] = Field(None, description="年龄组偏好")
    gender_preference: Optional[PatientGender] = Field(None, description="性别偏好")
    severity_preference: Optional[SeverityLevel] = Field(None, description="严重程度偏好")
    tags: Optional[List[str]] = Field(None, description="标签过滤")

class PatientGenerationResponse(BaseModel):
    """患者生成响应模型"""
    patient: VirtualPatientPrompt = Field(..., description="生成的虚拟患者")
    session_id: str = Field(..., description="会话ID")
    avatar_url: Optional[str] = Field(None, description="数字人头像URL")
    voice_config: Optional[Dict[str, Any]] = Field(None, description="语音配置")
