#!/bin/bash

# 虚拟患者对话系统 Electron 构建脚本
# 用于构建跨平台桌面应用

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 检查Node.js版本
check_node_version() {
    local required_version="16.0.0"
    local current_version=$(node -v | sed 's/v//')
    
    if [ "$(printf '%s\n' "$required_version" "$current_version" | sort -V | head -n1)" != "$required_version" ]; then
        log_error "Node.js版本过低，需要 >= $required_version，当前版本: $current_version"
        exit 1
    fi
    
    log_success "Node.js版本检查通过: $current_version"
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    rm -rf temp_build
}

# 设置陷阱，确保脚本退出时清理
trap cleanup EXIT

# 主函数
main() {
    log_info "开始构建虚拟患者对话系统桌面版..."
    
    # 检查必要的命令
    check_command "node"
    check_command "npm"
    check_command "git"
    
    # 检查Node.js版本
    check_node_version
    
    # 获取脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
    
    log_info "项目根目录: $PROJECT_ROOT"
    
    # 进入项目根目录
    cd "$PROJECT_ROOT"
    
    # 解析命令行参数
    PLATFORM="all"
    CLEAN=false
    SKIP_FRONTEND=false
    SKIP_BACKEND=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --platform)
                PLATFORM="$2"
                shift 2
                ;;
            --clean)
                CLEAN=true
                shift
                ;;
            --skip-frontend)
                SKIP_FRONTEND=true
                shift
                ;;
            --skip-backend)
                SKIP_BACKEND=true
                shift
                ;;
            --help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --platform <win|linux|mac|all>  指定构建平台 (默认: all)"
                echo "  --clean                          清理构建目录"
                echo "  --skip-frontend                  跳过前端构建"
                echo "  --skip-backend                   跳过后端构建"
                echo "  --help                           显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    log_info "构建平台: $PLATFORM"
    
    # 清理构建目录
    if [ "$CLEAN" = true ]; then
        log_info "清理构建目录..."
        rm -rf electron/dist
        rm -rf frontend/dist
        rm -rf backend/dist
        log_success "构建目录清理完成"
    fi
    
    # 1. 构建前端
    if [ "$SKIP_FRONTEND" = false ]; then
        log_info "构建前端应用..."
        cd frontend
        
        if [ ! -d "node_modules" ]; then
            log_info "安装前端依赖..."
            npm install
        fi
        
        log_info "构建前端生产版本..."
        npm run build
        
        if [ ! -d "dist" ]; then
            log_error "前端构建失败，dist目录不存在"
            exit 1
        fi
        
        log_success "前端构建完成"
        cd ..
    else
        log_warning "跳过前端构建"
    fi
    
    # 2. 构建后端
    if [ "$SKIP_BACKEND" = false ]; then
        log_info "构建后端应用..."
        cd backend
        
        # 检查Python环境
        if ! command -v python3 &> /dev/null; then
            log_error "Python3未找到，请先安装Python3"
            exit 1
        fi
        
        # 创建虚拟环境（如果不存在）
        if [ ! -d "venv" ]; then
            log_info "创建Python虚拟环境..."
            python3 -m venv venv
        fi
        
        # 激活虚拟环境
        source venv/bin/activate
        
        # 安装依赖
        log_info "安装后端依赖..."
        pip install -r requirements.txt
        
        # 安装PyInstaller
        pip install pyinstaller
        
        # 构建可执行文件
        log_info "构建后端可执行文件..."
        pyinstaller --onefile \
                   --name main \
                   --distpath dist \
                   --workpath build \
                   --specpath . \
                   --add-data "app:app" \
                   --hidden-import uvicorn \
                   --hidden-import fastapi \
                   --hidden-import motor \
                   app/main.py
        
        if [ ! -f "dist/main" ] && [ ! -f "dist/main.exe" ]; then
            log_error "后端构建失败，可执行文件不存在"
            exit 1
        fi
        
        log_success "后端构建完成"
        cd ..
    else
        log_warning "跳过后端构建"
    fi
    
    # 3. 准备Electron构建
    log_info "准备Electron构建..."
    cd electron
    
    # 安装Electron依赖
    if [ ! -d "node_modules" ]; then
        log_info "安装Electron依赖..."
        npm install
    fi
    
    # 复制前端构建结果
    if [ "$SKIP_FRONTEND" = false ]; then
        log_info "复制前端构建结果..."
        rm -rf dist
        cp -r ../frontend/dist .
        log_success "前端文件复制完成"
    fi
    
    # 4. 构建Electron应用
    log_info "构建Electron应用..."
    
    case $PLATFORM in
        "win")
            log_info "构建Windows版本..."
            npm run build-win
            ;;
        "linux")
            log_info "构建Linux版本..."
            npm run build-linux
            ;;
        "mac")
            log_info "构建macOS版本..."
            npm run build-mac
            ;;
        "all")
            log_info "构建所有平台版本..."
            npm run build
            ;;
        *)
            log_error "不支持的平台: $PLATFORM"
            exit 1
            ;;
    esac
    
    # 5. 检查构建结果
    log_info "检查构建结果..."
    
    if [ -d "dist" ]; then
        log_success "Electron构建完成！"
        log_info "构建文件位置: $(pwd)/dist"
        
        # 显示构建文件信息
        echo ""
        log_info "构建文件列表:"
        ls -la dist/
        
        # 计算文件大小
        total_size=$(du -sh dist/ | cut -f1)
        log_info "总大小: $total_size"
        
    else
        log_error "Electron构建失败，dist目录不存在"
        exit 1
    fi
    
    cd ..
    
    # 6. 生成构建报告
    log_info "生成构建报告..."
    
    cat > build_report.txt << EOF
虚拟患者对话系统构建报告
========================

构建时间: $(date)
构建平台: $PLATFORM
项目版本: $(cat package.json | grep '"version"' | cut -d'"' -f4)

构建组件:
- 前端: $([ "$SKIP_FRONTEND" = false ] && echo "✓" || echo "跳过")
- 后端: $([ "$SKIP_BACKEND" = false ] && echo "✓" || echo "跳过")
- Electron: ✓

构建文件:
$(ls -la electron/dist/ 2>/dev/null || echo "无构建文件")

总大小: $(du -sh electron/dist/ 2>/dev/null | cut -f1 || echo "未知")

构建状态: 成功
EOF
    
    log_success "构建报告已生成: build_report.txt"
    
    # 7. 可选的后处理
    log_info "执行后处理..."
    
    # 创建安装包校验和
    if [ -d "electron/dist" ]; then
        cd electron/dist
        for file in *.exe *.dmg *.AppImage *.deb *.rpm; do
            if [ -f "$file" ]; then
                sha256sum "$file" > "$file.sha256"
                log_info "已生成校验和: $file.sha256"
            fi
        done
        cd ../..
    fi
    
    log_success "虚拟患者对话系统构建完成！"
    echo ""
    log_info "下一步:"
    log_info "1. 测试构建的应用程序"
    log_info "2. 上传到发布平台"
    log_info "3. 更新文档和发布说明"
}

# 运行主函数
main "$@"
