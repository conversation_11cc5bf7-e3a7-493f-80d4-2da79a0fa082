<template>
  <div class="analysis-result-container">
    <!-- 顶部导航 -->
    <div class="result-header">
      <h1 class="page-title">虚拟患者对话分析结果</h1>
      <div class="header-actions">
        <button class="btn btn-secondary" @click="exportToPDF">
          <i class="icon-pdf"></i>
          导出PDF
        </button>
        <button class="btn btn-secondary" @click="printReport">
          <i class="icon-print"></i>
          打印报告
        </button>
        <button class="btn btn-primary" @click="newSession">
          <i class="icon-new"></i>
          新的对话
        </button>
      </div>
    </div>
    <!-- 分析结果内容 -->
    <div class="result-content" v-if="analysisResult">
      <!-- 总体得分卡片 -->
      <div class="result-card overall-score-card">
        <h2 class="card-title">总体评估</h2>
        <div class="score-overview">
          <div class="score-circle" :class="getScoreClass(analysisResult.overall_score)">
            <span class="score-value">{{ Math.round(analysisResult.overall_score) }}</span>
            <span class="score-label">总分</span>
          </div>
          <div class="score-details">
            <div class="score-item">
              <span class="score-name">等级评定</span>
              <span class="score-grade" :class="getGradeClass(analysisResult.grade)">
                {{ analysisResult.grade }}
              </span>
            </div>
            <div class="score-item">
              <span class="score-name">分析时间</span>
              <span class="score-time">{{ formatDateTime(analysisResult.analysis_timestamp) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 五个分析维度 -->
      <div class="analysis-dimensions">
        <!-- 1. 情绪价值分析 -->
        <div class="result-card dimension-card">
          <h3 class="dimension-title">
            <i class="icon-emotion"></i>
            1. 给予的情绪价值
          </h3>
          <div class="dimension-score">
            <div class="score-bar">
              <div
                class="score-fill emotion-fill"
                :style="{ width: analysisResult.emotional_value_score + '%' }"
              ></div>
            </div>
            <span class="score-number">{{ analysisResult.emotional_value_score.toFixed(1) }}分</span>
          </div>
          <div class="dimension-content">
            <p class="dimension-feedback">
              {{ analysisResult.emotional_value.detailed_feedback }}
            </p>
            <div class="dimension-details">
              <div class="detail-grid">
                <div class="detail-item">
                  <span class="detail-label">共情能力:</span>
                  <span class="detail-value">{{ analysisResult.emotional_value.empathy_score }}分</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">情感支持:</span>
                  <span class="detail-value">{{ analysisResult.emotional_value.emotional_support }}分</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">沟通温暖度:</span>
                  <span class="detail-value">{{ analysisResult.emotional_value.communication_warmth }}分</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">患者舒适度:</span>
                  <span class="detail-value">{{ analysisResult.emotional_value.patient_comfort_level }}分</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 防抑郁结果正确度评估 -->
        <div class="result-section">
          <h3>2. 防抑郁结果正确度评估</h3>
          <div class="assessment-result">
            <p>抑郁风险评估: <span :class="['risk-level', `risk-${analysisResult.depressionRisk.level}`]">{{ analysisResult.depressionRisk.text }}</span></p>
            <p>评估可信度: {{ analysisResult.depressionRisk.confidence }}%</p>
            <p>{{ analysisResult.depressionRisk.description }}</p>
          </div>
        </div>
        
        <!-- 对话记录 -->
        <div class="result-section">
          <h3>3. 对话记录</h3>
          <div class="dialogue-record">
            <div v-for="(msg, index) in analysisResult.dialogueRecord" :key="index" class="dialogue-item">
              <div class="dialogue-speaker">{{ msg.speaker }}:</div>
              <div class="dialogue-content">{{ msg.content }}</div>
            </div>
          </div>
        </div>
        
        <!-- 身体建议 -->
        <div class="result-section">
          <h3>4. 身体建议</h3>
          <div class="recommendations">
            <div v-for="(rec, index) in analysisResult.recommendations" :key="index" class="recommendation-item">
              <div class="recommendation-title">{{ rec.title }}</div>
              <div class="recommendation-content">{{ rec.content }}</div>
            </div>
          </div>
        </div>
        
        <!-- 打分 -->
        <div class="result-section">
          <h3>5. 打分</h3>
          <div class="scoring">
            <div class="score-item" v-for="(score, key) in analysisResult.scores" :key="key">
              <div class="score-label">{{ scoreLabels[key] || key }}</div>
              <div class="score-value">
                <el-rate :model-value="score" disabled show-score text-color="#ff9900"></el-rate>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ResultPage',
  data() {
    return {
      // 聊天历史
      chatHistory: [
        { type: 'user', content: '最近工作压力很大，经常失眠，感觉很疲惫。', time: '14:25' },
        { type: 'system', content: '您的情绪分析已完成，正在生成报告...', time: '14:26' }
      ],
      
      // 情绪值（模拟数据）
      emotionValues: {
        positive: 25,
        negative: 45,
        neutral: 30
      },
      
      // 分析结果（模拟数据）
      analysisResult: {
        emotionSummary: '根据您的描述，我们检测到您目前处于轻度压力状态，情绪偏向消极。您的表述中包含了工作压力、睡眠问题和疲惫感，这些都是常见的压力反应。建议您关注自己的休息质量，并尝试一些减压方法。',
        
        depressionRisk: {
          level: 'medium',
          text: '中等风险',
          confidence: 75,
          description: '您的描述中包含一些抑郁情绪的指标，如持续的疲惫感和睡眠问题。这些可能是压力引起的暂时状态，但也建议您关注自己的情绪变化。如果这些症状持续超过两周，建议咨询专业心理医生。'
        },
        
        dialogueRecord: [
          { speaker: '用户', content: '最近工作压力很大，经常失眠，感觉很疲惫。' },
          { speaker: '系统', content: '您能否描述一下您的睡眠情况？例如，您大约几点入睡，夜间是否会醒来多次？' },
          { speaker: '用户', content: '通常晚上11点上床，但要躺1-2小时才能入睡，经常半夜醒来，然后很难再次入睡。' },
          { speaker: '系统', content: '了解了。您平时有什么放松或减压的方式吗？' },
          { speaker: '用户', content: '没有特别的方式，下班后通常看看手机或电视。' }
        ],
        
        recommendations: [
          { 
            title: '改善睡眠质量', 
            content: '尝试建立规律的睡眠时间表，睡前1小时避免使用电子设备，可以尝试冥想或深呼吸练习来帮助入睡。' 
          },
          { 
            title: '身体活动', 
            content: '每天进行30分钟的中等强度运动，如散步、游泳或瑜伽，有助于减轻压力和改善睡眠。' 
          },
          { 
            title: '饮食建议', 
            content: '减少咖啡因和酒精摄入，增加富含镁的食物（如坚果、绿叶蔬菜）有助于放松神经系统。' 
          },
          { 
            title: '工作休息平衡', 
            content: '工作时每45-60分钟短暂休息5-10分钟，使用番茄工作法可以提高效率并减轻压力。' 
          }
        ],
        
        scores: {
          emotionalHealth: 3,
          sleepQuality: 2,
          stressLevel: 4,
          overallWellbeing: 3
        }
      },
      
      // 评分标签
      scoreLabels: {
        emotionalHealth: '情绪健康',
        sleepQuality: '睡眠质量',
        stressLevel: '压力水平',
        overallWellbeing: '整体健康'
      }
    };
  },
  mounted() {
    // 获取URL参数中的分析ID
    const analysisId = this.$route.query.id;
    if (analysisId) {
      // 实际项目中，这里应该根据ID从后端获取分析结果
      // this.fetchAnalysisResult(analysisId);
      console.log('分析ID:', analysisId);
    }
  },
  methods: {
    // 返回分析页面
    goBack() {
      this.$router.push('/analysis');
    },

    // 开始新的对话
    newSession() {
      this.$router.push('/');
    },
    
    // 获取分析结果（实际项目中应从后端获取）
    async fetchAnalysisResult(id) {
      try {
        // const response = await this.$axios.get(`/api/analysis-results/${id}`);
        // this.analysisResult = response.data;
        console.log('获取分析结果:', id);
      } catch (error) {
        console.error('获取分析结果失败:', error);
        this.$message.error('获取分析结果失败');
      }
    },

    // 导出PDF
    async exportToPDF() {
      try {
        this.$message.info('正在生成PDF报告...');

        // 获取当前会话ID
        const sessionId = localStorage.getItem('sessionId') || 'demo-session';

        // 调用后端PDF生成接口
        const response = await this.$axios.post(`/api/pdf/generate/${sessionId}`, {
          template_type: 'detailed',
          include_charts: true
        }, {
          responseType: 'blob'
        });

        // 创建下载链接
        const blob = new Blob([response.data], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `虚拟患者对话分析报告_${new Date().toISOString().split('T')[0]}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$message.success('PDF报告已下载');
      } catch (error) {
        console.error('PDF导出失败:', error);
        this.$message.error('PDF导出失败，请稍后重试');
      }
    },

    // 打印报告
    printReport() {
      try {
        // 隐藏不需要打印的元素
        const headerActions = document.querySelector('.header-actions');
        if (headerActions) {
          headerActions.style.display = 'none';
        }

        // 执行打印
        window.print();

        // 恢复隐藏的元素
        setTimeout(() => {
          if (headerActions) {
            headerActions.style.display = 'flex';
          }
        }, 100);

        this.$message.success('打印任务已发送');
      } catch (error) {
        console.error('打印失败:', error);
        this.$message.error('打印失败，请稍后重试');
      }
    }
  }
};
</script>

<style scoped>
.result-container {
  display: flex;
  height: 100vh;
  background-color: #f5f7fa;
}

.user-info-panel {
  width: 300px;
  background-color: white;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.user-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}

.user-text {
  text-align: center;
  margin-bottom: 20px;
}

.chat-history-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-top: 1px solid #e6e6e6;
  padding-top: 15px;
}

.chat-history-content {
  flex: 1;
  overflow-y: auto;
  margin-top: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.chat-message {
  margin-bottom: 12px;
  max-width: 85%;
  padding: 8px 12px;
  border-radius: 8px;
  position: relative;
}

.user-message {
  align-self: flex-end;
  background-color: #e1f3ff;
  margin-left: auto;
}

.system-message {
  align-self: flex-start;
  background-color: #f0f0f0;
}

.message-content {
  word-break: break-word;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  text-align: right;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow: hidden;
}

.top-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.result-report {
  flex: 1;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 30px;
  overflow-y: auto;
}

.report-title {
  text-align: center;
  margin-bottom: 30px;
  font-size: 24px;
  color: #333;
}

.result-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.result-section h3 {
  margin-bottom: 15px;
  color: #333;
}

.emotion-chart {
  margin-bottom: 20px;
}

.placeholder-chart {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
}

.emotion-bar {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.emotion-label {
  width: 60px;
  font-weight: bold;
}

.emotion-value {
  height: 24px;
  background-color: #67c23a;
  color: white;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 10px;
  border-radius: 4px;
  transition: width 0.5s ease;
}

.emotion-value.negative {
  background-color: #f56c6c;
}

.emotion-value.neutral {
  background-color: #909399;
}

.emotion-summary {
  line-height: 1.6;
  color: #666;
}

.assessment-result {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  line-height: 1.6;
}

.risk-level {
  font-weight: bold;
}

.risk-low {
  color: #67c23a;
}

.risk-medium {
  color: #e6a23c;
}

.risk-high {
  color: #f56c6c;
}

.dialogue-record {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.dialogue-item {
  margin-bottom: 10px;
  display: flex;
}

.dialogue-speaker {
  font-weight: bold;
  margin-right: 10px;
  min-width: 40px;
}

.dialogue-content {
  flex: 1;
  line-height: 1.5;
}

.recommendations {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.recommendation-item {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
}

.recommendation-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #409eff;
}

.recommendation-content {
  line-height: 1.5;
  color: #666;
}

.scoring {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.score-item {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.score-label {
  margin-bottom: 10px;
  font-weight: bold;
}

/* 按钮动画效果 */
.action-btn {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.action-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
}

.action-btn:hover::before {
  left: 100%;
}

/* 为不同按钮添加特殊效果 */
.export-btn:hover {
  color: #67c23a;
  border-color: #67c23a;
}

.print-btn:hover {
  color: #409eff;
  border-color: #409eff;
}

.btn-secondary {
  background-color: #f5f7fa;
  color: #606266;
  border: 1px solid #dcdfe6;
  margin-right: 10px;
}

.btn-secondary:hover {
  background-color: #ecf5ff;
  color: #409eff;
  border-color: #c6e2ff;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.icon-pdf::before {
  content: "📄";
}

.icon-print::before {
  content: "🖨️";
}

.icon-new::before {
  content: "➕";
}

.new-btn:hover {
  color: #e6a23c;
  border-color: #e6a23c;
}

/* 添加结果区域的动画效果 */
.result-section {
  transition: all 0.5s ease;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s forwards;
  animation-delay: calc(var(--index, 0) * 0.2s);
}

.result-section:nth-child(1) { --index: 1; }
.result-section:nth-child(2) { --index: 2; }
.result-section:nth-child(3) { --index: 3; }
.result-section:nth-child(4) { --index: 4; }
.result-section:nth-child(5) { --index: 5; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 打印样式 */
@media print {
  .user-info-panel,
  .top-nav {
    display: none;
  }
  
  .main-content {
    padding: 0;
  }
  
  .result-report {
    box-shadow: none;
    padding: 0;
  }
}
</style>