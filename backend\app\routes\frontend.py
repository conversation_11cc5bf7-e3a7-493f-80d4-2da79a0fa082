"""
前端路由 - 使用 Jinja2 模板渲染前端页面
"""

from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
import os

router = APIRouter(tags=["前端页面"])

# 配置模板目录
# 获取backend目录的绝对路径
backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
templates_dir = os.path.join(backend_dir, "templates")
print(f"模板目录路径: {templates_dir}")  # 调试信息
templates = Jinja2Templates(directory=templates_dir)

@router.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """首页"""
    return templates.TemplateResponse("index.html", {"request": request})

@router.get("/conversation", response_class=HTMLResponse)
async def conversation(request: Request):
    """对话页面"""
    return templates.TemplateResponse("index.html", {"request": request})

@router.get("/result", response_class=HTMLResponse)
async def result(request: Request):
    """结果页面"""
    return templates.TemplateResponse("index.html", {"request": request})
