# 虚拟患者对话系统部署指南

本文档提供了虚拟患者对话系统的完整部署指南，包括开发环境、生产环境和Electron桌面应用的部署方案。

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    部署架构图                                │
├─────────────────────────────────────────────────────────────┤
│  前端层 (Frontend)                                          │
│  ├─ Vue.js 应用 (开发: Vite, 生产: Nginx)                   │
│  ├─ Electron 桌面应用 (Windows/Linux)                       │
│  └─ 静态资源 (CDN/本地)                                     │
├─────────────────────────────────────────────────────────────┤
│  API网关层 (API Gateway)                                    │
│  ├─ Nginx 反向代理                                          │
│  ├─ 负载均衡                                               │
│  └─ SSL终止                                                │
├─────────────────────────────────────────────────────────────┤
│  应用层 (Application)                                       │
│  ├─ FastAPI 后端服务 (端口: 8000)                          │
│  ├─ AI模型服务集群 (端口: 8001-8008)                       │
│  └─ 语音服务集群 (端口: 8005-8007)                         │
├─────────────────────────────────────────────────────────────┤
│  数据层 (Data)                                             │
│  ├─ MongoDB 数据库                                         │
│  ├─ Redis 缓存                                             │
│  └─ 文件存储 (本地/云存储)                                  │
└─────────────────────────────────────────────────────────────┘
```

## 1. 环境要求

### 1.1 硬件要求

#### 最小配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **GPU**: 可选（AI模型加速）

#### 推荐配置
- **CPU**: 8核心以上
- **内存**: 16GB RAM以上
- **存储**: 100GB SSD以上
- **GPU**: NVIDIA RTX 3060以上（AI模型）

### 1.2 软件要求

#### 基础环境
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Windows 10+
- **Python**: 3.9+
- **Node.js**: 16+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

#### 数据库
- **MongoDB**: 5.0+
- **Redis**: 6.0+

## 2. 开发环境部署

### 2.1 克隆项目
```bash
git clone https://github.com/your-org/virtual-patient-system.git
cd virtual-patient-system
```

### 2.2 后端环境配置

```bash
# 创建虚拟环境
cd backend
python -m venv venv

# 激活虚拟环境
# Linux/Mac
source venv/bin/activate
# Windows
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等
```

**backend/.env**:
```bash
# 数据库配置
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=virtual_patient_dev
REDIS_URL=redis://localhost:6379

# AI模型配置
OPENAI_API_KEY=your_openai_api_key
AZURE_SPEECH_KEY=your_azure_speech_key
AZURE_SPEECH_REGION=your_region

# 服务配置
AVATAR_SERVICE_URL=http://localhost:8001
CONVERSATION_SERVICE_URL=http://localhost:8002
ANALYSIS_SERVICE_URL=http://localhost:8003
DIAGNOSIS_SERVICE_URL=http://localhost:8004

# 安全配置
SECRET_KEY=your_secret_key_here
CORS_ORIGINS=["http://localhost:3000"]

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

### 2.3 前端环境配置

```bash
# 安装依赖
cd frontend
npm install

# 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 文件
```

**frontend/.env.local**:
```bash
VITE_API_BASE_URL=http://localhost:8000/api
VITE_WS_URL=ws://localhost:8000/ws
VITE_STATIC_URL=http://localhost:8000/static
```

### 2.4 启动开发服务

```bash
# 启动数据库服务
docker-compose -f docker-compose.dev.yml up -d mongodb redis

# 启动后端服务
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 启动前端服务
cd frontend
npm run dev
```

## 3. 生产环境部署

### 3.1 Docker部署（推荐）

#### 3.1.1 构建镜像

**docker-compose.prod.yml**:
```yaml
version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    ports:
      - "8000:8000"
    environment:
      - MONGODB_URL=mongodb://mongodb:27017
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
    depends_on:
      - mongodb
      - redis

  # AI模型服务
  avatar-service:
    build: ./ai-models/avatar
    ports:
      - "8001:8001"
    volumes:
      - ./models:/app/models
      - ./storage/avatars:/app/storage/avatars
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  conversation-ai:
    build: ./ai-models/conversation
    ports:
      - "8002:8002"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}

  analysis-ai:
    build: ./ai-models/analysis
    ports:
      - "8003:8003"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}

  diagnosis-ai:
    build: ./ai-models/diagnosis
    ports:
      - "8004:8004"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}

  # 语音服务
  whisper-service:
    build: ./voice-models/whisper
    ports:
      - "8007:8007"
    volumes:
      - ./models/whisper:/app/models

  # 数据库服务
  mongodb:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongodb/mongod.conf:/etc/mongod.conf
    command: mongod --config /etc/mongod.conf

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/etc/redis/redis.conf
    command: redis-server /etc/redis/redis.conf

  # 监控服务
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning

volumes:
  mongodb_data:
  redis_data:
  grafana_data:
```

#### 3.1.2 Nginx配置

**nginx/nginx.conf**:
```nginx
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 上游服务器
    upstream backend {
        server backend:8000;
    }

    upstream ai_services {
        server avatar-service:8001;
        server conversation-ai:8002;
        server analysis-ai:8003;
        server diagnosis-ai:8004;
    }

    # HTTP重定向到HTTPS
    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    # HTTPS服务器
    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        # SSL配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        # 前端静态文件
        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri $uri/ /index.html;
        }

        # API代理
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket代理
        location /ws/ {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
        }

        # 静态资源
        location /static/ {
            alias /app/storage/;
            expires 1d;
            add_header Cache-Control "public, immutable";
        }

        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    }
}
```

#### 3.1.3 部署脚本

**deploy.sh**:
```bash
#!/bin/bash

set -e

echo "开始部署虚拟患者对话系统..."

# 检查环境
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose未安装"
    exit 1
fi

# 创建必要目录
mkdir -p storage/avatars
mkdir -p storage/audio
mkdir -p logs
mkdir -p ssl

# 构建并启动服务
echo "构建Docker镜像..."
docker-compose -f docker-compose.prod.yml build

echo "启动服务..."
docker-compose -f docker-compose.prod.yml up -d

# 等待服务启动
echo "等待服务启动..."
sleep 30

# 健康检查
echo "进行健康检查..."
if curl -f http://localhost:8000/health; then
    echo "后端服务启动成功"
else
    echo "错误: 后端服务启动失败"
    exit 1
fi

if curl -f http://localhost/; then
    echo "前端服务启动成功"
else
    echo "错误: 前端服务启动失败"
    exit 1
fi

echo "部署完成！"
echo "访问地址: https://your-domain.com"
```

## 4. Electron桌面应用部署

### 4.1 构建配置

**electron/package.json**:
```json
{
  "name": "virtual-patient-system",
  "version": "1.0.0",
  "description": "虚拟患者对话系统桌面版",
  "main": "main.js",
  "scripts": {
    "start": "electron .",
    "build": "electron-builder",
    "build-win": "electron-builder --win",
    "build-linux": "electron-builder --linux",
    "pack": "electron-builder --dir",
    "dist": "npm run build"
  },
  "build": {
    "appId": "com.yourcompany.virtual-patient",
    "productName": "虚拟患者对话系统",
    "directories": {
      "output": "dist"
    },
    "files": [
      "main.js",
      "preload.js",
      "dist/**/*",
      "node_modules/**/*"
    ],
    "win": {
      "target": "nsis",
      "icon": "assets/icon.ico"
    },
    "linux": {
      "target": "AppImage",
      "icon": "assets/icon.png",
      "category": "Education"
    },
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true,
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true
    }
  }
}
```

### 4.2 主进程配置

**electron/main.js**:
```javascript
const { app, BrowserWindow, Menu, dialog } = require('electron');
const path = require('path');
const { spawn } = require('child_process');

let mainWindow;
let backendProcess;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets/icon.png')
  });

  // 加载应用
  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadURL('http://localhost:3000');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, 'dist/index.html'));
  }

  // 设置菜单
  const menu = Menu.buildFromTemplate([
    {
      label: '文件',
      submenu: [
        {
          label: '退出',
          accelerator: 'CmdOrCtrl+Q',
          click: () => app.quit()
        }
      ]
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于',
              message: '虚拟患者对话系统',
              detail: '版本 1.0.0\n用于医学教育的虚拟患者对话训练系统'
            });
          }
        }
      ]
    }
  ]);
  Menu.setApplicationMenu(menu);
}

function startBackend() {
  // 启动内置后端服务
  const backendPath = path.join(__dirname, 'backend', 'main.exe');
  if (require('fs').existsSync(backendPath)) {
    backendProcess = spawn(backendPath);
    console.log('后端服务已启动');
  }
}

app.whenReady().then(() => {
  startBackend();
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (backendProcess) {
    backendProcess.kill();
  }
  if (process.platform !== 'darwin') {
    app.quit();
  }
});
```

### 4.3 构建脚本

**build-electron.sh**:
```bash
#!/bin/bash

echo "构建Electron应用..."

# 构建前端
cd frontend
npm run build
cp -r dist ../electron/

# 构建后端可执行文件
cd ../backend
pip install pyinstaller
pyinstaller --onefile --name main app/main.py
cp dist/main ../electron/backend/

# 构建Electron应用
cd ../electron
npm install
npm run build

echo "构建完成！"
echo "Windows安装包: electron/dist/虚拟患者对话系统 Setup 1.0.0.exe"
echo "Linux应用包: electron/dist/虚拟患者对话系统-1.0.0.AppImage"
```

## 5. 监控和维护

### 5.1 日志管理

**logging.conf**:
```ini
[loggers]
keys=root,app

[handlers]
keys=consoleHandler,fileHandler

[formatters]
keys=simpleFormatter

[logger_root]
level=INFO
handlers=consoleHandler

[logger_app]
level=INFO
handlers=consoleHandler,fileHandler
qualname=app
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=simpleFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=handlers.RotatingFileHandler
level=INFO
formatter=simpleFormatter
args=('logs/app.log', 'a', 10485760, 5)

[formatter_simpleFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(message)s
```

### 5.2 备份策略

**backup.sh**:
```bash
#!/bin/bash

BACKUP_DIR="/backup/virtual-patient"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mongodump --host localhost:27017 --db virtual_patient --out $BACKUP_DIR/mongodb_$DATE

# 备份文件存储
tar -czf $BACKUP_DIR/storage_$DATE.tar.gz storage/

# 清理旧备份（保留7天）
find $BACKUP_DIR -name "mongodb_*" -mtime +7 -exec rm -rf {} \;
find $BACKUP_DIR -name "storage_*.tar.gz" -mtime +7 -delete

echo "备份完成: $BACKUP_DIR"
```

## 6. 故障排除

### 6.1 常见问题

1. **服务启动失败**
   - 检查端口占用: `netstat -tulpn | grep :8000`
   - 检查日志: `docker-compose logs backend`

2. **AI模型调用失败**
   - 检查API密钥配置
   - 检查网络连接
   - 查看模型服务日志

3. **数据库连接失败**
   - 检查MongoDB服务状态
   - 验证连接字符串
   - 检查防火墙设置

### 6.2 性能优化

1. **数据库优化**
   - 创建适当的索引
   - 定期清理过期数据
   - 监控查询性能

2. **缓存优化**
   - 配置Redis缓存策略
   - 实现API响应缓存
   - 优化静态资源缓存

3. **服务器优化**
   - 调整worker进程数
   - 配置负载均衡
   - 监控资源使用情况

通过以上部署指南，可以在各种环境中成功部署虚拟患者对话系统。
