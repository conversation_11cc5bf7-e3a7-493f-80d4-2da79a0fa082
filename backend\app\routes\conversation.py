from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional
import logging

from ..models.conversation import (
    ConversationSession,
    ConversationMessage,
    ConversationRequest,
    ConversationResponse,
    ConversationStatus,
    MessageType,
    AudioProcessingRequest,
    AudioProcessingResponse,
    TTSRequest,
    TTSResponse
)
from ..services.conversation_service import conversation_service
from ..services.ai_service import ai_service
from ..services.audio_service import audio_service

router = APIRouter(prefix="/conversation", tags=["对话管理"])
logger = logging.getLogger(__name__)

@router.post("/start", response_model=ConversationSession)
async def start_conversation(session_id: str, patient_id: str):
    """
    开始新的对话会话
    """
    try:
        session = await conversation_service.create_session(session_id, patient_id)
        return session
    except Exception as e:
        logger.error(f"开始对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"开始对话失败: {str(e)}")

@router.post("/message", response_model=ConversationResponse)
async def send_message(request: ConversationRequest):
    """
    发送消息并获取患者回复
    """
    try:
        # 获取会话信息
        session = await conversation_service.get_session(request.session_id)
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")
        
        if session.status != ConversationStatus.ACTIVE:
            raise HTTPException(status_code=400, detail="会话已结束")
        
        # 处理音频输入（如果有）
        if request.audio_data:
            audio_text = await audio_service.speech_to_text(
                AudioProcessingRequest(
                    audio_data=request.audio_data,
                    session_id=request.session_id
                )
            )
            # 如果音频转文字成功，使用转换后的文本
            if audio_text.text:
                request.message = audio_text.text
        
        # 保存医生消息
        doctor_message = await conversation_service.add_message(
            session_id=request.session_id,
            message_type=MessageType.DOCTOR,
            content=request.message,
            round_number=session.current_round + 1
        )
        
        # 获取AI患者回复
        patient_response = await ai_service.get_patient_response(
            session_id=request.session_id,
            doctor_message=request.message,
            conversation_history=session.messages
        )
        
        # 生成患者回复的语音
        audio_url = None
        if session.enable_voice:
            tts_response = await audio_service.text_to_speech(
                TTSRequest(
                    text=patient_response,
                    session_id=request.session_id
                )
            )
            audio_url = tts_response.audio_url
        
        # 保存患者回复
        patient_message = await conversation_service.add_message(
            session_id=request.session_id,
            message_type=MessageType.PATIENT,
            content=patient_response,
            audio_url=audio_url,
            round_number=session.current_round + 1
        )
        
        # 更新会话轮数
        updated_session = await conversation_service.update_round(request.session_id)
        
        # 检查是否达到最大轮数
        is_complete = updated_session.current_round >= updated_session.max_rounds
        if is_complete:
            await conversation_service.end_session(request.session_id)
        
        # 获取下一步建议（可选）
        next_suggestions = None
        if not is_complete:
            next_suggestions = await ai_service.get_conversation_suggestions(
                session_id=request.session_id,
                conversation_history=updated_session.messages
            )
        
        return ConversationResponse(
            patient_response=patient_response,
            audio_url=audio_url,
            emotional_tone=patient_message.emotional_tone or "neutral",
            round_number=updated_session.current_round,
            is_conversation_complete=is_complete,
            next_suggestions=next_suggestions
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送消息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发送消息失败: {str(e)}")

@router.get("/session/{session_id}", response_model=ConversationSession)
async def get_session(session_id: str):
    """
    获取会话信息
    """
    try:
        session = await conversation_service.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")
        return session
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取会话信息失败: {str(e)}")

@router.get("/session/{session_id}/messages", response_model=List[ConversationMessage])
async def get_messages(session_id: str, limit: int = 50, offset: int = 0):
    """
    获取会话消息列表
    """
    try:
        messages = await conversation_service.get_messages(session_id, limit, offset)
        return messages
    except Exception as e:
        logger.error(f"获取消息列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取消息列表失败: {str(e)}")

@router.post("/session/{session_id}/end")
async def end_conversation(session_id: str):
    """
    结束对话会话
    """
    try:
        session = await conversation_service.end_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")
        return {"message": "对话已结束", "session": session}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"结束对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"结束对话失败: {str(e)}")

@router.post("/session/{session_id}/pause")
async def pause_conversation(session_id: str):
    """
    暂停对话会话
    """
    try:
        session = await conversation_service.pause_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")
        return {"message": "对话已暂停", "session": session}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"暂停对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"暂停对话失败: {str(e)}")

@router.post("/session/{session_id}/resume")
async def resume_conversation(session_id: str):
    """
    恢复对话会话
    """
    try:
        session = await conversation_service.resume_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")
        return {"message": "对话已恢复", "session": session}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"恢复对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"恢复对话失败: {str(e)}")

@router.post("/audio/stt", response_model=AudioProcessingResponse)
async def speech_to_text(request: AudioProcessingRequest):
    """
    语音转文字 (STT)
    """
    try:
        result = await audio_service.speech_to_text(request)
        return result
    except Exception as e:
        logger.error(f"语音转文字失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"语音转文字失败: {str(e)}")

@router.post("/audio/tts", response_model=TTSResponse)
async def text_to_speech(request: TTSRequest):
    """
    文字转语音 (TTS)
    """
    try:
        result = await audio_service.text_to_speech(request)
        return result
    except Exception as e:
        logger.error(f"文字转语音失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文字转语音失败: {str(e)}")

@router.get("/sessions", response_model=List[ConversationSession])
async def get_sessions(
    status: Optional[ConversationStatus] = None,
    limit: int = 20,
    offset: int = 0
):
    """
    获取会话列表
    """
    try:
        sessions = await conversation_service.get_sessions(status, limit, offset)
        return sessions
    except Exception as e:
        logger.error(f"获取会话列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")
