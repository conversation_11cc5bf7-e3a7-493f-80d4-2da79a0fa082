# 项目名称：情绪分析助手

## 项目概述
这是一个基于Electron的跨平台应用，用于情绪分析和记录。前端使用Vue.js框架，后端采用FastAPI，数据存储在MongoDB中。

## 技术栈
- **前端**：Electron, Vue.js, HTML, CSS, JavaScript
- **后端**：FastAPI (Python)
- **数据库**：MongoDB

## 项目结构
```
├── frontend/                # 前端Electron+Vue应用
│   ├── public/              # 静态资源
│   ├── src/                 # 源代码
│   │   ├── assets/          # 资源文件
│   │   ├── components/      # Vue组件
│   │   ├── views/           # 页面视图
│   │   ├── App.vue          # 主应用组件
│   │   ├── background.js    # Electron主进程
│   │   ├── main.js          # Vue入口文件
│   │   └── router.js        # 路由配置
│   ├── package.json         # 依赖配置
│   └── vue.config.js        # Vue配置
├── backend/                 # 后端FastAPI应用
│   ├── app/                 # 应用代码
│   │   ├── models/          # 数据模型
│   │   ├── routes/          # API路由
│   │   ├── services/        # 业务逻辑
│   │   ├── utils/           # 工具函数
│   │   └── main.py          # 主应用入口
│   ├── requirements.txt     # Python依赖
│   └── Dockerfile           # 后端Docker配置
└── README.md                # 项目说明
```

## 安装与运行

### 前端
```bash
cd frontend
npm install
npm run electron:serve  # 开发模式
npm run electron:build  # 构建应用
```

### 后端
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload  # 开发模式运行
```

## 功能特性
- 情绪分析记录
- 防抑郁结果正确度评估
- 对话记录功能
- 身体建议提供
- 数据导出与打印

## 开发注意事项
- 后端模型部分预留接口，不进行实现
- 应用需支持Windows和Linux跨平台运行