@echo off
REM 虚拟患者对话系统 Electron 构建脚本 (Windows)
REM 用于在Windows环境下构建桌面应用

setlocal enabledelayedexpansion

REM 设置代码页为UTF-8
chcp 65001 >nul

REM 颜色定义（Windows 10+）
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 日志函数
:log_info
echo %BLUE%[INFO]%NC% %~1
goto :eof

:log_success
echo %GREEN%[SUCCESS]%NC% %~1
goto :eof

:log_warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:log_error
echo %RED%[ERROR]%NC% %~1
goto :eof

REM 检查命令是否存在
:check_command
where %1 >nul 2>&1
if errorlevel 1 (
    call :log_error "%1 命令未找到，请先安装"
    exit /b 1
)
goto :eof

REM 检查Node.js版本
:check_node_version
for /f "tokens=*" %%i in ('node -v') do set NODE_VERSION=%%i
set NODE_VERSION=%NODE_VERSION:v=%

REM 简单版本检查（假设需要16.0.0+）
for /f "tokens=1 delims=." %%a in ("%NODE_VERSION%") do set MAJOR_VERSION=%%a
if %MAJOR_VERSION% LSS 16 (
    call :log_error "Node.js版本过低，需要 >= 16.0.0，当前版本: %NODE_VERSION%"
    exit /b 1
)

call :log_success "Node.js版本检查通过: %NODE_VERSION%"
goto :eof

REM 主函数
:main
call :log_info "开始构建虚拟患者对话系统桌面版..."

REM 检查必要的命令
call :check_command node
if errorlevel 1 exit /b 1

call :check_command npm
if errorlevel 1 exit /b 1

call :check_command git
if errorlevel 1 exit /b 1

REM 检查Node.js版本
call :check_node_version
if errorlevel 1 exit /b 1

REM 获取脚本所在目录
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."

call :log_info "项目根目录: %PROJECT_ROOT%"

REM 进入项目根目录
cd /d "%PROJECT_ROOT%"

REM 解析命令行参数
set "PLATFORM=all"
set "CLEAN=false"
set "SKIP_FRONTEND=false"
set "SKIP_BACKEND=false"

:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="--platform" (
    set "PLATFORM=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--clean" (
    set "CLEAN=true"
    shift
    goto :parse_args
)
if "%~1"=="--skip-frontend" (
    set "SKIP_FRONTEND=true"
    shift
    goto :parse_args
)
if "%~1"=="--skip-backend" (
    set "SKIP_BACKEND=true"
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    echo 用法: %0 [选项]
    echo 选项:
    echo   --platform ^<win^|linux^|mac^|all^>  指定构建平台 ^(默认: all^)
    echo   --clean                          清理构建目录
    echo   --skip-frontend                  跳过前端构建
    echo   --skip-backend                   跳过后端构建
    echo   --help                           显示帮助信息
    exit /b 0
)
call :log_error "未知参数: %~1"
exit /b 1

:args_done
call :log_info "构建平台: %PLATFORM%"

REM 清理构建目录
if "%CLEAN%"=="true" (
    call :log_info "清理构建目录..."
    if exist "electron\dist" rmdir /s /q "electron\dist"
    if exist "frontend\dist" rmdir /s /q "frontend\dist"
    if exist "backend\dist" rmdir /s /q "backend\dist"
    call :log_success "构建目录清理完成"
)

REM 1. 构建前端
if "%SKIP_FRONTEND%"=="false" (
    call :log_info "构建前端应用..."
    cd frontend
    
    if not exist "node_modules" (
        call :log_info "安装前端依赖..."
        npm install
        if errorlevel 1 (
            call :log_error "前端依赖安装失败"
            exit /b 1
        )
    )
    
    call :log_info "构建前端生产版本..."
    npm run build
    if errorlevel 1 (
        call :log_error "前端构建失败"
        exit /b 1
    )
    
    if not exist "dist" (
        call :log_error "前端构建失败，dist目录不存在"
        exit /b 1
    )
    
    call :log_success "前端构建完成"
    cd ..
) else (
    call :log_warning "跳过前端构建"
)

REM 2. 构建后端
if "%SKIP_BACKEND%"=="false" (
    call :log_info "构建后端应用..."
    cd backend
    
    REM 检查Python环境
    where python >nul 2>&1
    if errorlevel 1 (
        call :log_error "Python未找到，请先安装Python"
        exit /b 1
    )
    
    REM 创建虚拟环境（如果不存在）
    if not exist "venv" (
        call :log_info "创建Python虚拟环境..."
        python -m venv venv
        if errorlevel 1 (
            call :log_error "虚拟环境创建失败"
            exit /b 1
        )
    )
    
    REM 激活虚拟环境
    call venv\Scripts\activate.bat
    
    REM 安装依赖
    call :log_info "安装后端依赖..."
    pip install -r requirements.txt
    if errorlevel 1 (
        call :log_error "后端依赖安装失败"
        exit /b 1
    )
    
    REM 安装PyInstaller
    pip install pyinstaller
    if errorlevel 1 (
        call :log_error "PyInstaller安装失败"
        exit /b 1
    )
    
    REM 构建可执行文件
    call :log_info "构建后端可执行文件..."
    pyinstaller --onefile ^
               --name main ^
               --distpath dist ^
               --workpath build ^
               --specpath . ^
               --add-data "app;app" ^
               --hidden-import uvicorn ^
               --hidden-import fastapi ^
               --hidden-import motor ^
               app\main.py
    
    if errorlevel 1 (
        call :log_error "后端构建失败"
        exit /b 1
    )
    
    if not exist "dist\main.exe" (
        call :log_error "后端构建失败，可执行文件不存在"
        exit /b 1
    )
    
    call :log_success "后端构建完成"
    cd ..
) else (
    call :log_warning "跳过后端构建"
)

REM 3. 准备Electron构建
call :log_info "准备Electron构建..."
cd electron

REM 安装Electron依赖
if not exist "node_modules" (
    call :log_info "安装Electron依赖..."
    npm install
    if errorlevel 1 (
        call :log_error "Electron依赖安装失败"
        exit /b 1
    )
)

REM 复制前端构建结果
if "%SKIP_FRONTEND%"=="false" (
    call :log_info "复制前端构建结果..."
    if exist "dist" rmdir /s /q "dist"
    xcopy /e /i "..\frontend\dist" "dist"
    if errorlevel 1 (
        call :log_error "前端文件复制失败"
        exit /b 1
    )
    call :log_success "前端文件复制完成"
)

REM 4. 构建Electron应用
call :log_info "构建Electron应用..."

if "%PLATFORM%"=="win" (
    call :log_info "构建Windows版本..."
    npm run build-win
) else if "%PLATFORM%"=="linux" (
    call :log_info "构建Linux版本..."
    npm run build-linux
) else if "%PLATFORM%"=="mac" (
    call :log_info "构建macOS版本..."
    npm run build-mac
) else if "%PLATFORM%"=="all" (
    call :log_info "构建所有平台版本..."
    npm run build
) else (
    call :log_error "不支持的平台: %PLATFORM%"
    exit /b 1
)

if errorlevel 1 (
    call :log_error "Electron构建失败"
    exit /b 1
)

REM 5. 检查构建结果
call :log_info "检查构建结果..."

if exist "dist" (
    call :log_success "Electron构建完成！"
    call :log_info "构建文件位置: %cd%\dist"
    
    REM 显示构建文件信息
    echo.
    call :log_info "构建文件列表:"
    dir dist
    
) else (
    call :log_error "Electron构建失败，dist目录不存在"
    exit /b 1
)

cd ..

REM 6. 生成构建报告
call :log_info "生成构建报告..."

echo 虚拟患者对话系统构建报告 > build_report.txt
echo ======================== >> build_report.txt
echo. >> build_report.txt
echo 构建时间: %date% %time% >> build_report.txt
echo 构建平台: %PLATFORM% >> build_report.txt
echo. >> build_report.txt
echo 构建组件: >> build_report.txt
if "%SKIP_FRONTEND%"=="false" (
    echo - 前端: ✓ >> build_report.txt
) else (
    echo - 前端: 跳过 >> build_report.txt
)
if "%SKIP_BACKEND%"=="false" (
    echo - 后端: ✓ >> build_report.txt
) else (
    echo - 后端: 跳过 >> build_report.txt
)
echo - Electron: ✓ >> build_report.txt
echo. >> build_report.txt
echo 构建状态: 成功 >> build_report.txt

call :log_success "构建报告已生成: build_report.txt"

REM 7. 可选的后处理
call :log_info "执行后处理..."

REM 创建安装包校验和
if exist "electron\dist" (
    cd electron\dist
    for %%f in (*.exe *.msi) do (
        if exist "%%f" (
            certutil -hashfile "%%f" SHA256 > "%%f.sha256"
            call :log_info "已生成校验和: %%f.sha256"
        )
    )
    cd ..\..
)

call :log_success "虚拟患者对话系统构建完成！"
echo.
call :log_info "下一步:"
call :log_info "1. 测试构建的应用程序"
call :log_info "2. 上传到发布平台"
call :log_info "3. 更新文档和发布说明"

goto :eof

REM 调用主函数
call :main %*
