from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Dict, Any, Optional
import uvicorn
import os
import json
from datetime import datetime
import logging

# 导入自定义模块
from app.routes import health, virtual_patient_analysis
from app.routes.virtual_patient import router as virtual_patient_router
from app.routes.conversation import router as conversation_router
from app.routes.conversation_analysis import router as conversation_analysis_router
from app.routes.pdf_export import router as pdf_export_router
from app.routes.monitoring import router as monitoring_router
from app.routes.frontend import router as frontend_router
from app.services.database import init_database, close_database
from app.services.virtual_patient_service import virtual_patient_service

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用实例
app = FastAPI(
    title="虚拟患者系统API",
    description="虚拟患者对话训练系统的后端API服务",
    version="1.0.0"
)

# 配置CORS中间件，允许前端访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含路由模块
# 前端路由（不加前缀，直接处理根路径）
app.include_router(frontend_router)
# API路由
app.include_router(health.router)
app.include_router(virtual_patient_analysis.router, prefix="/api")
app.include_router(virtual_patient_router, prefix="/api")
app.include_router(conversation_router, prefix="/api")
app.include_router(conversation_analysis_router, prefix="/api")
app.include_router(pdf_export_router, prefix="/api")
app.include_router(monitoring_router, prefix="/api")

# 应用启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    try:
        logger.info("正在初始化数据库...")
        await init_database()

        logger.info("正在创建示例患者数据...")
        await virtual_patient_service.create_sample_patients()

        logger.info("应用启动完成")
    except Exception as e:
        logger.error(f"应用启动失败: {str(e)}")
        raise

# 应用关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理操作"""
    try:
        logger.info("正在关闭数据库连接...")
        await close_database()
        logger.info("应用关闭完成")
    except Exception as e:
        logger.error(f"应用关闭失败: {str(e)}")



# 启动服务器的入口点（直接运行此文件时使用）
if __name__ == "__main__":
    # 确定端口，优先使用环境变量中的端口
    port = int(os.environ.get("PORT", 8000))
    
    # 启动服务器
    uvicorn.run("app.main:app", host="0.0.0.0", port=port, reload=True)