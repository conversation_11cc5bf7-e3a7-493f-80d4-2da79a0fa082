/**
 * Electron预加载脚本
 * 
 * 在渲染进程中安全地暴露Node.js API
 */

const { contextBridge, ipcRenderer } = require('electron');

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 应用信息
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  getAppPath: () => ipcRenderer.invoke('get-app-path'),
  
  // 对话框
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  
  // 文件操作
  saveFile: (filePath, content) => ipcRenderer.invoke('save-file', filePath, content),
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  
  // PDF操作
  exportToPDF: (options) => ipcRenderer.invoke('export-to-pdf', options),
  printPage: (options) => ipcRenderer.invoke('print-page', options),
  
  // 系统操作
  openExternal: (url) => ipcRenderer.invoke('open-external', url),
  showItemInFolder: (path) => ipcRenderer.invoke('show-item-in-folder', path),
  
  // 窗口操作
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),
  
  // 菜单事件监听
  onMenuAction: (callback) => {
    ipcRenderer.on('menu-new-session', callback);
    ipcRenderer.on('menu-export-pdf', callback);
    ipcRenderer.on('menu-settings', callback);
  },
  
  // 移除监听器
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
  
  // 应用设置
  getSettings: () => ipcRenderer.invoke('get-settings'),
  setSettings: (settings) => ipcRenderer.invoke('set-settings', settings),
  
  // 数据库操作
  executeQuery: (query, params) => ipcRenderer.invoke('execute-query', query, params),
  
  // 网络状态
  isOnline: () => navigator.onLine,
  onNetworkChange: (callback) => {
    window.addEventListener('online', () => callback(true));
    window.addEventListener('offline', () => callback(false));
  },
  
  // 通知
  showNotification: (title, options) => {
    if (Notification.permission === 'granted') {
      return new Notification(title, options);
    } else if (Notification.permission !== 'denied') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          return new Notification(title, options);
        }
      });
    }
  },
  
  // 剪贴板
  writeToClipboard: (text) => ipcRenderer.invoke('write-to-clipboard', text),
  readFromClipboard: () => ipcRenderer.invoke('read-from-clipboard'),
  
  // 语音功能
  startVoiceRecording: () => ipcRenderer.invoke('start-voice-recording'),
  stopVoiceRecording: () => ipcRenderer.invoke('stop-voice-recording'),
  playAudio: (audioData) => ipcRenderer.invoke('play-audio', audioData),
  
  // 主题
  setTheme: (theme) => ipcRenderer.invoke('set-theme', theme),
  getTheme: () => ipcRenderer.invoke('get-theme'),
  
  // 日志
  log: {
    info: (message) => ipcRenderer.invoke('log-info', message),
    warn: (message) => ipcRenderer.invoke('log-warn', message),
    error: (message) => ipcRenderer.invoke('log-error', message)
  },
  
  // 更新
  checkForUpdates: () => ipcRenderer.invoke('check-for-updates'),
  onUpdateAvailable: (callback) => ipcRenderer.on('update-available', callback),
  onUpdateDownloaded: (callback) => ipcRenderer.on('update-downloaded', callback),
  
  // 开发工具
  isDev: () => ipcRenderer.invoke('is-dev'),
  openDevTools: () => ipcRenderer.invoke('open-dev-tools'),
  
  // 性能监控
  getSystemInfo: () => ipcRenderer.invoke('get-system-info'),
  getMemoryUsage: () => ipcRenderer.invoke('get-memory-usage'),
  
  // 安全
  validateInput: (input, type) => ipcRenderer.invoke('validate-input', input, type),
  sanitizeHtml: (html) => ipcRenderer.invoke('sanitize-html', html)
});

// 暴露平台信息
contextBridge.exposeInMainWorld('platform', {
  isWindows: process.platform === 'win32',
  isMac: process.platform === 'darwin',
  isLinux: process.platform === 'linux',
  arch: process.arch,
  version: process.version
});

// 暴露环境变量（安全的）
contextBridge.exposeInMainWorld('env', {
  NODE_ENV: process.env.NODE_ENV || 'production',
  APP_VERSION: process.env.npm_package_version
});

// 错误处理
window.addEventListener('error', (event) => {
  ipcRenderer.invoke('log-error', {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    error: event.error?.stack
  });
});

window.addEventListener('unhandledrejection', (event) => {
  ipcRenderer.invoke('log-error', {
    message: 'Unhandled Promise Rejection',
    reason: event.reason
  });
});

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', () => {
  // 添加平台特定的CSS类
  document.body.classList.add(`platform-${process.platform}`);
  
  // 设置应用版本信息
  ipcRenderer.invoke('get-app-version').then(version => {
    document.documentElement.setAttribute('data-app-version', version);
  });
  
  // 初始化主题
  ipcRenderer.invoke('get-theme').then(theme => {
    document.documentElement.setAttribute('data-theme', theme || 'light');
  });
});

// 键盘快捷键处理
document.addEventListener('keydown', (event) => {
  // Ctrl/Cmd + R: 刷新页面
  if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
    event.preventDefault();
    location.reload();
  }
  
  // F11: 全屏切换
  if (event.key === 'F11') {
    event.preventDefault();
    ipcRenderer.invoke('toggle-fullscreen');
  }
  
  // F12: 开发者工具
  if (event.key === 'F12') {
    event.preventDefault();
    ipcRenderer.invoke('toggle-dev-tools');
  }
  
  // Ctrl/Cmd + Shift + I: 开发者工具
  if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'I') {
    event.preventDefault();
    ipcRenderer.invoke('toggle-dev-tools');
  }
});

// 拖拽文件处理
document.addEventListener('dragover', (event) => {
  event.preventDefault();
});

document.addEventListener('drop', (event) => {
  event.preventDefault();
  
  const files = Array.from(event.dataTransfer.files);
  if (files.length > 0) {
    // 发送文件信息到主进程
    ipcRenderer.invoke('handle-dropped-files', files.map(file => ({
      name: file.name,
      path: file.path,
      size: file.size,
      type: file.type
    })));
  }
});

// 右键菜单处理
document.addEventListener('contextmenu', (event) => {
  // 在输入框中显示系统右键菜单
  if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
    return;
  }
  
  event.preventDefault();
  ipcRenderer.invoke('show-context-menu', {
    x: event.clientX,
    y: event.clientY
  });
});

// 链接处理
document.addEventListener('click', (event) => {
  if (event.target.tagName === 'A' && event.target.href) {
    const url = new URL(event.target.href);
    
    // 外部链接在系统浏览器中打开
    if (url.origin !== window.location.origin) {
      event.preventDefault();
      ipcRenderer.invoke('open-external', event.target.href);
    }
  }
});

// 窗口焦点处理
window.addEventListener('focus', () => {
  document.body.classList.add('window-focused');
});

window.addEventListener('blur', () => {
  document.body.classList.remove('window-focused');
});

// 在线状态处理
window.addEventListener('online', () => {
  document.body.classList.remove('offline');
  document.body.classList.add('online');
});

window.addEventListener('offline', () => {
  document.body.classList.remove('online');
  document.body.classList.add('offline');
});

// 初始化在线状态
if (navigator.onLine) {
  document.body.classList.add('online');
} else {
  document.body.classList.add('offline');
}

console.log('Preload script loaded successfully');
