@echo off
echo ========================================
echo 虚拟患者对话系统 - Electron 打包脚本
echo ========================================

echo.
echo 1. 激活虚拟环境并构建后端可执行文件...
cd backend
call .venv\Scripts\activate
python build.py
if errorlevel 1 (
    echo ❌ 后端构建失败
    pause
    exit /b 1
)
echo ✅ 后端构建完成

echo.
echo 2. 检查后端文件...
if not exist "dist\virtual-patient-system.exe" (
    echo ❌ 后端可执行文件不存在
    pause
    exit /b 1
)
if not exist "templates" (
    echo ❌ 模板目录不存在
    pause
    exit /b 1
)
echo ✅ 后端文件检查通过

cd ..

echo.
echo 3. 构建 Electron 应用...
cd frontend
call npm run electron:build
if errorlevel 1 (
    echo ❌ Electron 构建失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ 构建完成！
echo ========================================
echo.
echo 可执行文件位置:
echo   Windows: frontend\dist_electron\虚拟患者对话系统 Setup.exe
echo   或者: frontend\dist_electron\win-unpacked\虚拟患者对话系统.exe
echo.
echo 安装包位置: frontend\dist_electron\
echo.
pause
