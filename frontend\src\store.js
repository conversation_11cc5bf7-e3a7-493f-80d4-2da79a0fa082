import { createStore } from 'vuex'

export default createStore({
  state: {
    currentPatient: null,
    sessionId: null,
    analysisResult: null
  },
  mutations: {
    setCurrentPatient(state, patient) {
      state.currentPatient = patient
    },
    setSessionId(state, sessionId) {
      state.sessionId = sessionId
      // 同时保存到 localStorage
      localStorage.setItem('sessionId', sessionId)
    },
    setAnalysisResult(state, result) {
      state.analysisResult = result
      // 同时保存到 localStorage
      localStorage.setItem('analysisResult', JSON.stringify(result))
    },
    clearSession(state) {
      state.currentPatient = null
      state.sessionId = null
      state.analysisResult = null
      localStorage.removeItem('sessionId')
      localStorage.removeItem('analysisResult')
    }
  },
  actions: {
    initializeStore({ commit }) {
      // 从 localStorage 恢复数据
      const sessionId = localStorage.getItem('sessionId')
      const analysisResult = localStorage.getItem('analysisResult')
      
      if (sessionId) {
        commit('setSessionId', sessionId)
      }
      
      if (analysisResult) {
        try {
          commit('setAnalysisResult', JSON.parse(analysisResult))
        } catch (e) {
          console.warn('Failed to parse stored analysis result')
        }
      }
    }
  },
  getters: {
    hasActiveSession: (state) => !!state.sessionId,
    hasAnalysisResult: (state) => !!state.analysisResult
  }
})
