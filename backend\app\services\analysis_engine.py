"""
分析评估引擎

实现虚拟患者对话的5维度分析评估：
1. 给予的情绪价值 (25%)
2. 医生诊断的正确性 (30%)
3. 分配科室的正确性 (20%)
4. 对话质量分析 (15%)
5. 总体建议和评价 (10%)
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import statistics
from dataclasses import dataclass

from ..models.conversation import ConversationMessage
from ..models.virtual_patient import VirtualPatientPrompt
from .ai_model_manager import ai_model_manager

logger = logging.getLogger(__name__)

@dataclass
class AnalysisDimension:
    """分析维度数据类"""
    name: str
    score: float
    max_score: float
    weight: float
    detailed_feedback: str
    sub_scores: Dict[str, float]
    recommendations: List[str]

@dataclass
class AnalysisResult:
    """分析结果数据类"""
    session_id: str
    overall_score: float
    grade: str
    analysis_timestamp: datetime
    dimensions: Dict[str, AnalysisDimension]
    conversation_summary: str
    conversation_metadata: Dict[str, Any]
    key_interactions: List[Dict[str, Any]]
    overall_recommendations: List[str]

class AnalysisEngine:
    """分析评估引擎"""
    
    def __init__(self):
        # 评分权重配置
        self.dimension_weights = {
            "emotional_value": 0.25,      # 情绪价值 25%
            "diagnosis_accuracy": 0.30,   # 诊断正确性 30%
            "department_accuracy": 0.20,  # 科室分配 20%
            "conversation_quality": 0.15, # 对话质量 15%
            "overall_assessment": 0.10    # 总体评价 10%
        }
        
        # 评级标准
        self.grade_thresholds = {
            "优秀": 90,
            "良好": 80,
            "中等": 70,
            "及格": 60,
            "不及格": 0
        }
        
        # 关键词库
        self.empathy_keywords = [
            "理解", "担心", "关心", "感受", "辛苦", "不容易", 
            "放心", "别着急", "慢慢来", "我明白"
        ]
        
        self.medical_keywords = [
            "症状", "检查", "诊断", "治疗", "药物", "病史",
            "疼痛", "发热", "咳嗽", "头痛", "胸痛", "腹痛"
        ]
    
    async def analyze_conversation(
        self,
        session_id: str,
        conversation_messages: List[ConversationMessage],
        patient_prompt: VirtualPatientPrompt,
        doctor_diagnosis: Optional[str] = None,
        suggested_department: Optional[str] = None
    ) -> AnalysisResult:
        """
        分析对话并生成评估结果
        
        Args:
            session_id: 会话ID
            conversation_messages: 对话消息列表
            patient_prompt: 患者prompt信息
            doctor_diagnosis: 医生诊断
            suggested_department: 建议科室
            
        Returns:
            AnalysisResult: 分析结果
        """
        try:
            logger.info(f"开始分析会话 {session_id}")
            
            # 1. 分析情绪价值
            emotional_dimension = await self._analyze_emotional_value(
                conversation_messages, patient_prompt
            )
            
            # 2. 分析诊断正确性
            diagnosis_dimension = await self._analyze_diagnosis_accuracy(
                conversation_messages, patient_prompt, doctor_diagnosis
            )
            
            # 3. 分析科室分配正确性
            department_dimension = await self._analyze_department_accuracy(
                patient_prompt, suggested_department
            )
            
            # 4. 分析对话质量
            quality_dimension = await self._analyze_conversation_quality(
                conversation_messages, patient_prompt
            )
            
            # 5. 总体评价
            overall_dimension = await self._generate_overall_assessment(
                conversation_messages, patient_prompt
            )
            
            # 计算总分
            dimensions = {
                "emotional_value": emotional_dimension,
                "diagnosis_accuracy": diagnosis_dimension,
                "department_accuracy": department_dimension,
                "conversation_quality": quality_dimension,
                "overall_assessment": overall_dimension
            }
            
            overall_score = self._calculate_overall_score(dimensions)
            grade = self._determine_grade(overall_score)
            
            # 生成对话摘要和元数据
            conversation_summary = self._generate_conversation_summary(conversation_messages)
            conversation_metadata = self._extract_conversation_metadata(conversation_messages)
            key_interactions = self._identify_key_interactions(conversation_messages)
            overall_recommendations = self._generate_overall_recommendations(dimensions)
            
            # 创建分析结果
            result = AnalysisResult(
                session_id=session_id,
                overall_score=overall_score,
                grade=grade,
                analysis_timestamp=datetime.now(),
                dimensions=dimensions,
                conversation_summary=conversation_summary,
                conversation_metadata=conversation_metadata,
                key_interactions=key_interactions,
                overall_recommendations=overall_recommendations
            )
            
            logger.info(f"会话 {session_id} 分析完成，总分: {overall_score:.1f}")
            return result
            
        except Exception as e:
            logger.error(f"分析会话 {session_id} 失败: {str(e)}")
            raise
    
    async def _analyze_emotional_value(
        self, 
        messages: List[ConversationMessage], 
        patient_prompt: VirtualPatientPrompt
    ) -> AnalysisDimension:
        """分析情绪价值维度"""
        try:
            doctor_messages = [msg for msg in messages if msg.role == "doctor"]
            
            # 1. 共情能力分析
            empathy_score = self._calculate_empathy_score(doctor_messages)
            
            # 2. 情感支持分析
            emotional_support_score = self._calculate_emotional_support_score(doctor_messages)
            
            # 3. 沟通温暖度分析
            communication_warmth_score = self._calculate_communication_warmth_score(doctor_messages)
            
            # 4. 患者舒适度评估
            patient_comfort_score = self._calculate_patient_comfort_score(messages)
            
            # 计算综合得分
            sub_scores = {
                "empathy_score": empathy_score,
                "emotional_support": emotional_support_score,
                "communication_warmth": communication_warmth_score,
                "patient_comfort_level": patient_comfort_score
            }
            
            total_score = statistics.mean(sub_scores.values())
            
            # 生成详细反馈
            detailed_feedback = self._generate_emotional_feedback(sub_scores, total_score)
            
            # 生成建议
            recommendations = self._generate_emotional_recommendations(sub_scores)
            
            return AnalysisDimension(
                name="给予的情绪价值",
                score=total_score,
                max_score=100.0,
                weight=self.dimension_weights["emotional_value"],
                detailed_feedback=detailed_feedback,
                sub_scores=sub_scores,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"情绪价值分析失败: {str(e)}")
            return self._get_default_dimension("给予的情绪价值", "emotional_value")
    
    def _calculate_empathy_score(self, doctor_messages: List[ConversationMessage]) -> float:
        """计算共情能力得分"""
        if not doctor_messages:
            return 0.0
        
        empathy_count = 0
        total_messages = len(doctor_messages)
        
        for message in doctor_messages:
            content = message.content.lower()
            # 检查共情关键词
            for keyword in self.empathy_keywords:
                if keyword in content:
                    empathy_count += 1
                    break
        
        # 基础得分：共情关键词比例
        base_score = (empathy_count / total_messages) * 60
        
        # 加分项：问候语、安慰语
        bonus_score = 0
        first_message = doctor_messages[0].content.lower()
        if any(greeting in first_message for greeting in ["您好", "你好", "欢迎"]):
            bonus_score += 10
        
        # 检查是否有安慰性语言
        comfort_patterns = ["别担心", "会好的", "我们会帮助", "放心"]
        for message in doctor_messages:
            if any(pattern in message.content for pattern in comfort_patterns):
                bonus_score += 10
                break
        
        return min(base_score + bonus_score, 100.0)
    
    def _calculate_emotional_support_score(self, doctor_messages: List[ConversationMessage]) -> float:
        """计算情感支持得分"""
        if not doctor_messages:
            return 0.0
        
        support_indicators = 0
        total_messages = len(doctor_messages)
        
        support_patterns = [
            r"我理解.*感受",
            r"这确实.*不容易",
            r"您的.*很重要",
            r"我们会.*帮助",
            r"别.*担心",
            r"会.*好转"
        ]
        
        for message in doctor_messages:
            content = message.content
            for pattern in support_patterns:
                if re.search(pattern, content):
                    support_indicators += 1
                    break
        
        # 基础得分
        base_score = (support_indicators / max(total_messages, 1)) * 80
        
        # 检查是否有积极的语言
        positive_words = ["希望", "信心", "乐观", "积极", "康复"]
        for message in doctor_messages:
            if any(word in message.content for word in positive_words):
                base_score += 10
                break
        
        return min(base_score, 100.0)
    
    def _calculate_communication_warmth_score(self, doctor_messages: List[ConversationMessage]) -> float:
        """计算沟通温暖度得分"""
        if not doctor_messages:
            return 0.0
        
        warmth_score = 70.0  # 基础分
        
        # 检查礼貌用语
        polite_words = ["请", "谢谢", "不好意思", "麻烦", "辛苦"]
        polite_count = 0
        for message in doctor_messages:
            for word in polite_words:
                if word in message.content:
                    polite_count += 1
                    break
        
        warmth_score += min(polite_count * 5, 20)
        
        # 检查是否使用敬语
        formal_patterns = ["您", "请问", "能否", "是否可以"]
        for message in doctor_messages:
            if any(pattern in message.content for pattern in formal_patterns):
                warmth_score += 5
                break
        
        # 扣分项：冷漠或生硬的表达
        cold_patterns = ["必须", "应该", "不行", "不可以"]
        for message in doctor_messages:
            if any(pattern in message.content for pattern in cold_patterns):
                warmth_score -= 5
        
        return max(min(warmth_score, 100.0), 0.0)
    
    def _calculate_patient_comfort_score(self, messages: List[ConversationMessage]) -> float:
        """计算患者舒适度得分"""
        patient_messages = [msg for msg in messages if msg.role == "patient"]
        
        if not patient_messages:
            return 70.0  # 默认分数
        
        comfort_score = 70.0
        
        # 分析患者回复的情绪倾向
        positive_indicators = ["好的", "明白", "谢谢", "放心", "理解"]
        negative_indicators = ["不舒服", "担心", "害怕", "紧张", "焦虑"]
        
        positive_count = 0
        negative_count = 0
        
        for message in patient_messages:
            content = message.content
            if any(indicator in content for indicator in positive_indicators):
                positive_count += 1
            if any(indicator in content for indicator in negative_indicators):
                negative_count += 1
        
        # 根据患者情绪调整分数
        comfort_score += positive_count * 5
        comfort_score -= negative_count * 3
        
        return max(min(comfort_score, 100.0), 0.0)
    
    def _generate_emotional_feedback(self, sub_scores: Dict[str, float], total_score: float) -> str:
        """生成情绪价值详细反馈"""
        feedback_parts = []
        
        if total_score >= 85:
            feedback_parts.append("您在情绪价值方面表现优秀。")
        elif total_score >= 70:
            feedback_parts.append("您在情绪价值方面表现良好。")
        else:
            feedback_parts.append("您在情绪价值方面还有提升空间。")
        
        # 具体维度反馈
        if sub_scores["empathy_score"] < 60:
            feedback_parts.append("建议在对话中多表达理解和关心。")
        
        if sub_scores["emotional_support"] < 60:
            feedback_parts.append("可以给予患者更多的情感支持和鼓励。")
        
        if sub_scores["communication_warmth"] < 70:
            feedback_parts.append("建议使用更温暖、礼貌的语言与患者交流。")
        
        return " ".join(feedback_parts)
    
    def _generate_emotional_recommendations(self, sub_scores: Dict[str, float]) -> List[str]:
        """生成情绪价值改进建议"""
        recommendations = []
        
        if sub_scores["empathy_score"] < 70:
            recommendations.append("多使用'我理解您的感受'、'这确实不容易'等共情表达")
        
        if sub_scores["emotional_support"] < 70:
            recommendations.append("给予患者更多鼓励，如'我们会帮助您'、'情况会好转的'")
        
        if sub_scores["communication_warmth"] < 70:
            recommendations.append("使用更多礼貌用语，如'请'、'谢谢'、'不好意思'")
        
        if sub_scores["patient_comfort_level"] < 70:
            recommendations.append("注意观察患者情绪，及时给予安慰和支持")
        
        if not recommendations:
            recommendations.append("继续保持良好的沟通风格，注意倾听患者需求")
        
        return recommendations
    
    async def _analyze_diagnosis_accuracy(
        self,
        messages: List[ConversationMessage],
        patient_prompt: VirtualPatientPrompt,
        doctor_diagnosis: Optional[str]
    ) -> AnalysisDimension:
        """分析诊断正确性维度"""
        try:
            correct_diagnosis = patient_prompt.correct_diagnosis
            
            if not doctor_diagnosis:
                # 从对话中提取可能的诊断
                doctor_diagnosis = self._extract_diagnosis_from_conversation(messages)
            
            # 1. 诊断准确性评分
            accuracy_score = self._calculate_diagnosis_accuracy_score(
                doctor_diagnosis, correct_diagnosis
            )
            
            # 2. 诊断推理过程评分
            reasoning_score = self._calculate_reasoning_quality_score(messages, patient_prompt)
            
            # 3. 症状识别完整性评分
            symptom_recognition_score = self._calculate_symptom_recognition_score(
                messages, patient_prompt
            )
            
            # 4. 鉴别诊断考虑
            differential_diagnosis_score = self._calculate_differential_diagnosis_score(messages)
            
            sub_scores = {
                "accuracy_score": accuracy_score,
                "reasoning_quality": reasoning_score,
                "symptom_recognition": symptom_recognition_score,
                "differential_diagnosis": differential_diagnosis_score
            }
            
            total_score = statistics.mean(sub_scores.values())
            
            detailed_feedback = self._generate_diagnosis_feedback(
                sub_scores, doctor_diagnosis, correct_diagnosis
            )
            
            recommendations = self._generate_diagnosis_recommendations(sub_scores)
            
            return AnalysisDimension(
                name="医生诊断的正确性",
                score=total_score,
                max_score=100.0,
                weight=self.dimension_weights["diagnosis_accuracy"],
                detailed_feedback=detailed_feedback,
                sub_scores=sub_scores,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"诊断正确性分析失败: {str(e)}")
            return self._get_default_dimension("医生诊断的正确性", "diagnosis_accuracy")
    
    def _calculate_diagnosis_accuracy_score(
        self, 
        doctor_diagnosis: Optional[str], 
        correct_diagnosis: str
    ) -> float:
        """计算诊断准确性得分"""
        if not doctor_diagnosis:
            return 0.0
        
        # 简单的字符串相似度比较
        doctor_diag_clean = doctor_diagnosis.lower().strip()
        correct_diag_clean = correct_diagnosis.lower().strip()
        
        if doctor_diag_clean == correct_diag_clean:
            return 100.0
        elif correct_diag_clean in doctor_diag_clean or doctor_diag_clean in correct_diag_clean:
            return 80.0
        else:
            # 可以实现更复杂的相似度算法
            return 40.0
    
    def _extract_diagnosis_from_conversation(self, messages: List[ConversationMessage]) -> Optional[str]:
        """从对话中提取诊断"""
        doctor_messages = [msg for msg in messages if msg.role == "doctor"]
        
        diagnosis_patterns = [
            r"诊断.*?是.*?([^。，！？\n]+)",
            r"可能.*?是.*?([^。，！？\n]+)",
            r"考虑.*?([^。，！？\n]+)",
            r"初步.*?诊断.*?([^。，！？\n]+)"
        ]
        
        for message in reversed(doctor_messages):  # 从最后的消息开始查找
            content = message.content
            for pattern in diagnosis_patterns:
                match = re.search(pattern, content)
                if match:
                    return match.group(1).strip()
        
        return None
    
    def _get_default_dimension(self, name: str, dimension_key: str) -> AnalysisDimension:
        """获取默认维度评分"""
        return AnalysisDimension(
            name=name,
            score=70.0,
            max_score=100.0,
            weight=self.dimension_weights[dimension_key],
            detailed_feedback="分析过程中出现错误，使用默认评分。",
            sub_scores={},
            recommendations=["建议重新进行分析评估"]
        )
    
    def _calculate_overall_score(self, dimensions: Dict[str, AnalysisDimension]) -> float:
        """计算总体得分"""
        weighted_sum = 0.0
        total_weight = 0.0
        
        for dimension in dimensions.values():
            weighted_sum += dimension.score * dimension.weight
            total_weight += dimension.weight
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0
    
    def _determine_grade(self, score: float) -> str:
        """确定评级"""
        for grade, threshold in self.grade_thresholds.items():
            if score >= threshold:
                return grade
        return "不及格"
    
    def _generate_conversation_summary(self, messages: List[ConversationMessage]) -> str:
        """生成对话摘要"""
        if not messages:
            return "无对话记录"
        
        total_rounds = len([msg for msg in messages if msg.role == "doctor"])
        patient_messages = len([msg for msg in messages if msg.role == "patient"])
        
        return f"本次对话共进行了{total_rounds}轮问诊，患者回复{patient_messages}次。医生通过系统性问诊收集了患者的主要症状和病史信息。"
    
    def _extract_conversation_metadata(self, messages: List[ConversationMessage]) -> Dict[str, Any]:
        """提取对话元数据"""
        doctor_messages = [msg for msg in messages if msg.role == "doctor"]
        patient_messages = [msg for msg in messages if msg.role == "patient"]
        
        return {
            "total_rounds": len(doctor_messages),
            "message_count": len(messages),
            "conversation_duration": 0,  # 需要根据时间戳计算
            "avg_message_length": statistics.mean([len(msg.content) for msg in messages]) if messages else 0
        }
    
    def _identify_key_interactions(self, messages: List[ConversationMessage]) -> List[Dict[str, Any]]:
        """识别关键交互时刻"""
        key_interactions = []
        
        # 识别重要的问诊时刻
        important_keywords = ["症状", "疼痛", "检查", "诊断", "治疗", "药物"]
        
        for i, message in enumerate(messages):
            if message.role == "doctor":
                for keyword in important_keywords:
                    if keyword in message.content:
                        key_interactions.append({
                            "round": (i // 2) + 1,
                            "content": f"医生询问{keyword}相关信息",
                            "importance": "high"
                        })
                        break
        
        return key_interactions[:5]  # 返回最多5个关键时刻
    
    def _generate_overall_recommendations(self, dimensions: Dict[str, AnalysisDimension]) -> List[str]:
        """生成总体建议"""
        recommendations = []
        
        # 根据各维度得分生成建议
        for dimension in dimensions.values():
            if dimension.score < 70:
                recommendations.extend(dimension.recommendations[:1])  # 每个维度取一个建议
        
        # 添加通用建议
        if not recommendations:
            recommendations.append("继续保持良好的问诊技巧，注意与患者的沟通交流")
        
        recommendations.append("建议多练习不同类型的病例，提高诊断能力")
        recommendations.append("注意在问诊过程中体现人文关怀")
        
        return recommendations[:5]  # 最多返回5个建议

    def _calculate_reasoning_quality_score(
        self,
        messages: List[ConversationMessage],
        patient_prompt: VirtualPatientPrompt
    ) -> float:
        """计算推理过程质量得分"""
        doctor_messages = [msg for msg in messages if msg.role == "doctor"]

        if not doctor_messages:
            return 0.0

        reasoning_score = 60.0  # 基础分

        # 检查是否有系统性问诊
        systematic_patterns = [
            "主要症状", "现病史", "既往史", "家族史", "个人史",
            "什么时候开始", "持续多长时间", "有什么诱因", "伴随症状"
        ]

        systematic_count = 0
        for message in doctor_messages:
            for pattern in systematic_patterns:
                if pattern in message.content:
                    systematic_count += 1
                    break

        reasoning_score += min(systematic_count * 5, 30)

        # 检查逻辑推理
        logical_patterns = ["因为", "所以", "考虑到", "根据", "结合", "综合"]
        for message in doctor_messages:
            if any(pattern in message.content for pattern in logical_patterns):
                reasoning_score += 5
                break

        return min(reasoning_score, 100.0)

    def _calculate_symptom_recognition_score(
        self,
        messages: List[ConversationMessage],
        patient_prompt: VirtualPatientPrompt
    ) -> float:
        """计算症状识别完整性得分"""
        doctor_messages = [msg for msg in messages if msg.role == "doctor"]

        # 从患者prompt中提取关键症状
        key_symptoms = self._extract_key_symptoms(patient_prompt)

        if not key_symptoms:
            return 80.0  # 如果无法提取症状，给默认分

        recognized_symptoms = 0
        for symptom in key_symptoms:
            for message in doctor_messages:
                if symptom in message.content:
                    recognized_symptoms += 1
                    break

        recognition_rate = recognized_symptoms / len(key_symptoms)
        return recognition_rate * 100

    def _extract_key_symptoms(self, patient_prompt: VirtualPatientPrompt) -> List[str]:
        """从患者prompt中提取关键症状"""
        symptoms = []

        # 从主诉中提取
        if patient_prompt.chief_complaint:
            symptoms.append(patient_prompt.chief_complaint)

        # 从现病史中提取
        if hasattr(patient_prompt, 'present_illness') and patient_prompt.present_illness:
            # 简单的症状关键词提取
            symptom_keywords = ["疼痛", "发热", "咳嗽", "头痛", "胸痛", "腹痛", "恶心", "呕吐"]
            for keyword in symptom_keywords:
                if keyword in patient_prompt.present_illness:
                    symptoms.append(keyword)

        return list(set(symptoms))  # 去重

    def _calculate_differential_diagnosis_score(self, messages: List[ConversationMessage]) -> float:
        """计算鉴别诊断考虑得分"""
        doctor_messages = [msg for msg in messages if msg.role == "doctor"]

        differential_patterns = [
            "鉴别", "排除", "还需要考虑", "可能是", "也有可能", "需要排除"
        ]

        differential_count = 0
        for message in doctor_messages:
            for pattern in differential_patterns:
                if pattern in message.content:
                    differential_count += 1
                    break

        # 基础分 + 鉴别诊断考虑
        base_score = 60.0
        bonus_score = min(differential_count * 10, 40)

        return base_score + bonus_score

    def _generate_diagnosis_feedback(
        self,
        sub_scores: Dict[str, float],
        doctor_diagnosis: Optional[str],
        correct_diagnosis: str
    ) -> str:
        """生成诊断正确性详细反馈"""
        feedback_parts = []

        if sub_scores["accuracy_score"] >= 80:
            feedback_parts.append("诊断准确性较高。")
        elif sub_scores["accuracy_score"] >= 60:
            feedback_parts.append("诊断基本正确，但还可以更精确。")
        else:
            feedback_parts.append("诊断准确性需要提高。")

        feedback_parts.append(f"正确诊断应为：{correct_diagnosis}")

        if doctor_diagnosis:
            feedback_parts.append(f"您的诊断：{doctor_diagnosis}")
        else:
            feedback_parts.append("未明确给出诊断结论。")

        if sub_scores["reasoning_quality"] < 70:
            feedback_parts.append("建议加强系统性问诊和逻辑推理。")

        return " ".join(feedback_parts)

    def _generate_diagnosis_recommendations(self, sub_scores: Dict[str, float]) -> List[str]:
        """生成诊断改进建议"""
        recommendations = []

        if sub_scores["accuracy_score"] < 80:
            recommendations.append("加强对疾病诊断标准的学习和掌握")

        if sub_scores["reasoning_quality"] < 70:
            recommendations.append("注重系统性问诊，按照现病史、既往史等顺序收集信息")

        if sub_scores["symptom_recognition"] < 70:
            recommendations.append("提高对关键症状的识别和重视程度")

        if sub_scores["differential_diagnosis"] < 70:
            recommendations.append("加强鉴别诊断思维，考虑多种可能性")

        return recommendations

# 创建全局分析引擎实例
analysis_engine = AnalysisEngine()
