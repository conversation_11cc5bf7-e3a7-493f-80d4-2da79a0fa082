from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime
import uuid

# 导入模型
from ..models.analysis import (
    VirtualPatientAnalysisRequest,
    VirtualPatientAnalysisResponse,
    VirtualPatientAnalysisResult,
    PDFExportRequest,
    PDFExportResponse
)

# 导入服务
from ..services.analysis_service import analysis_service
from ..services.pdf_service import pdf_service

# 创建路由器
router = APIRouter(prefix="/analysis", tags=["虚拟患者分析"])
logger = logging.getLogger(__name__)

@router.post("/virtual-patient/analyze", response_model=VirtualPatientAnalysisResponse)
async def analyze_virtual_patient_conversation(request: VirtualPatientAnalysisRequest):
    """
    虚拟患者对话分析接口
    
    分析医生与虚拟患者的对话，返回5个部分的综合评估结果：
    1. 给予的情绪价值
    2. 医生诊断的正确性
    3. 分配科室的正确性
    4. 聊天记录展示
    5. 总体建议和打分
    """
    try:
        # 调用虚拟患者分析服务
        result = await analysis_service.analyze_virtual_patient_conversation(
            session_id=request.session_id,
            conversation_data=request.conversation_data,
            patient_info=request.patient_info
        )
        
        return VirtualPatientAnalysisResponse(
            success=True,
            message="虚拟患者对话分析完成",
            result=result
        )
        
    except Exception as e:
        logger.error(f"虚拟患者对话分析失败: {str(e)}")
        return VirtualPatientAnalysisResponse(
            success=False,
            message=f"分析失败: {str(e)}",
            result=None
        )

@router.get("/virtual-patient/result/{session_id}", response_model=VirtualPatientAnalysisResponse)
async def get_virtual_patient_analysis_result(session_id: str):
    """
    获取虚拟患者对话分析结果
    
    根据会话ID返回分析结果
    """
    try:
        result = await analysis_service.get_virtual_patient_analysis_result(session_id)
        
        if result is None:
            return VirtualPatientAnalysisResponse(
                success=False,
                message="分析结果不存在",
                result=None
            )
        
        return VirtualPatientAnalysisResponse(
            success=True,
            message="获取分析结果成功",
            result=result
        )
        
    except Exception as e:
        logger.error(f"获取虚拟患者分析结果失败: {str(e)}")
        return VirtualPatientAnalysisResponse(
            success=False,
            message=f"获取分析结果失败: {str(e)}",
            result=None
        )

@router.post("/export/pdf", response_model=PDFExportResponse)
async def export_analysis_to_pdf(request: PDFExportRequest):
    """
    导出分析结果为PDF
    
    将分析结果生成专业的PDF报告
    """
    try:
        pdf_response = await pdf_service.generate_analysis_pdf(
            session_id=request.session_id,
            analysis_result=request.analysis_result,
            template_type=request.template_type,
            include_charts=request.include_charts
        )
        
        return pdf_response
        
    except Exception as e:
        logger.error(f"PDF导出失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"PDF导出失败: {str(e)}")

@router.get("/export/pdf/{session_id}")
async def download_pdf(session_id: str):
    """
    下载PDF文件
    
    根据会话ID下载生成的PDF报告
    """
    try:
        pdf_file = await pdf_service.get_pdf_file(session_id)
        
        if not pdf_file:
            raise HTTPException(status_code=404, detail="PDF文件不存在")
        
        return pdf_file
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"PDF下载失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"PDF下载失败: {str(e)}")

@router.post("/trigger/{session_id}")
async def trigger_analysis(session_id: str):
    """
    触发分析
    
    当对话达到10轮或用户点击提交分析按钮时触发
    """
    try:
        # 获取对话数据
        conversation_data = await analysis_service.get_conversation_data(session_id)
        patient_info = await analysis_service.get_patient_info(session_id)
        
        if not conversation_data:
            raise HTTPException(status_code=404, detail="对话数据不存在")
        
        # 创建分析请求
        analysis_request = VirtualPatientAnalysisRequest(
            session_id=session_id,
            conversation_data=conversation_data,
            patient_info=patient_info
        )
        
        # 执行分析
        result = await analysis_service.analyze_virtual_patient_conversation(
            session_id=analysis_request.session_id,
            conversation_data=analysis_request.conversation_data,
            patient_info=analysis_request.patient_info
        )
        
        return VirtualPatientAnalysisResponse(
            success=True,
            message="分析完成",
            result=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"触发分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"触发分析失败: {str(e)}")

@router.get("/sessions", response_model=List[Dict[str, Any]])
async def get_analysis_sessions(
    limit: int = 20,
    offset: int = 0,
    status: Optional[str] = None
):
    """
    获取分析会话列表
    """
    try:
        sessions = await analysis_service.get_analysis_sessions(
            limit=limit,
            offset=offset,
            status=status
        )
        return sessions
    except Exception as e:
        logger.error(f"获取分析会话列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分析会话列表失败: {str(e)}")

@router.delete("/result/{session_id}")
async def delete_analysis_result(session_id: str):
    """
    删除分析结果
    """
    try:
        success = await analysis_service.delete_analysis_result(session_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="分析结果不存在")
        
        return {"success": True, "message": "删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除分析结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@router.get("/statistics")
async def get_analysis_statistics():
    """
    获取分析统计信息
    """
    try:
        stats = await analysis_service.get_analysis_statistics()
        return stats
    except Exception as e:
        logger.error(f"获取分析统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分析统计信息失败: {str(e)}")

@router.post("/batch-analyze")
async def batch_analyze_conversations(session_ids: List[str]):
    """
    批量分析对话
    """
    try:
        results = await analysis_service.batch_analyze_conversations(session_ids)
        return {"success": True, "results": results}
    except Exception as e:
        logger.error(f"批量分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量分析失败: {str(e)}")

@router.get("/templates")
async def get_analysis_templates():
    """
    获取分析模板列表
    """
    try:
        templates = await analysis_service.get_analysis_templates()
        return templates
    except Exception as e:
        logger.error(f"获取分析模板失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分析模板失败: {str(e)}")

@router.post("/custom-analysis")
async def custom_analysis(
    session_id: str,
    analysis_config: Dict[str, Any]
):
    """
    自定义分析配置
    """
    try:
        result = await analysis_service.custom_analysis(session_id, analysis_config)
        return result
    except Exception as e:
        logger.error(f"自定义分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"自定义分析失败: {str(e)}")
