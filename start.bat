@echo off
echo 正在启动情绪分析助手...

REM 检查是否安装了Node.js
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到Node.js，请安装Node.js后再试。
    pause
    exit /b 1
)

REM 检查是否安装了Python
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到Python，请安装Python后再试。
    pause
    exit /b 1
)

REM 检查前端依赖
if not exist "frontend\node_modules" (
    echo 正在安装前端依赖...
    cd frontend
    call npm install
    if %ERRORLEVEL% neq 0 (
        echo 错误: 前端依赖安装失败。
        cd ..
        pause
        exit /b 1
    )
    cd ..
)

REM 检查后端依赖
echo 正在检查后端依赖...
cd backend
python -m pip install -r requirements.txt
if %ERRORLEVEL% neq 0 (
    echo 错误: 后端依赖安装失败。
    cd ..
    pause
    exit /b 1
)
cd ..

REM 启动应用
echo 正在启动应用...
cd frontend
start cmd /k "npm run electron:serve"
echo.
echo 如果应用未自动启动，请尝试以下命令：
echo 1. cd frontend
echo 2. npm run electron:serve

echo 应用启动成功！
pause