# 虚拟患者对话系统

<div align="center">

**基于AI的医学教育虚拟患者对话训练系统**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![Vue.js 3](https://img.shields.io/badge/vue.js-3.x-green.svg)](https://vuejs.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100+-red.svg)](https://fastapi.tiangolo.com/)

[功能特点](#功能特点) • [快速开始](#快速开始) • [部署指南](#部署) • [文档](#文档) • [贡献](#贡献指南)

</div>

## 📋 项目简介

虚拟患者对话系统是一个专为医学教育设计的AI驱动训练平台，帮助医学生和医生提高问诊技能。系统通过模拟真实的医患对话场景，提供智能化的分析评估和个性化的改进建议。

### 🎯 核心价值

- **提升问诊技能**: 通过与虚拟患者的对话练习，提高医生的沟通和诊断能力
- **标准化训练**: 提供一致的训练环境和评估标准
- **个性化反馈**: 基于AI分析的详细反馈和改进建议
- **安全环境**: 在无风险的环境中进行医疗技能训练

## ✨ 功能特点

### 🤖 智能对话系统
- **AI驱动的虚拟患者**: 基于GPT-4的自然语言对话
- **多样化病例**: 涵盖各科室的典型病例
- **个性化患者**: 不同年龄、性别、性格的患者角色
- **情绪模拟**: 真实的患者情绪和反应

### 🎭 数字人展示
- **3D数字人**: 基于Stable Diffusion生成的患者头像
- **表情动画**: 根据对话内容的表情变化
- **语音合成**: 自然的语音回复
- **视觉反馈**: 直观的患者状态显示

### 📊 智能分析评估
- **5维度评估**: 情绪价值、诊断正确性、科室分配、对话质量、总体表现
- **实时反馈**: 对话过程中的即时建议
- **详细报告**: 专业的分析报告和PDF导出
- **进步追踪**: 历史表现对比和趋势分析

### 🎤 语音交互
- **语音识别**: 支持中文语音输入
- **语音合成**: 自然的患者语音回复
- **Whisper集成**: 高精度的语音转文字
- **多语言支持**: 支持多种语言的语音交互

### 📱 现代化界面
- **响应式设计**: 适配桌面和移动设备
- **直观操作**: 简洁易用的用户界面
- **实时更新**: WebSocket实时通信
- **离线支持**: Electron桌面应用

## 🏗️ 技术架构

### 后端技术栈
- **FastAPI**: 高性能的Python Web框架
- **MongoDB**: 文档数据库，存储患者数据和对话记录
- **Redis**: 内存缓存，提升系统性能
- **OpenAI GPT-4**: 对话AI模型
- **Whisper**: 语音识别模型
- **Azure Speech**: 语音合成服务

### 前端技术栈
- **Vue.js 3**: 现代化的前端框架
- **Vite**: 快速的构建工具
- **TypeScript**: 类型安全的JavaScript
- **Element Plus**: UI组件库
- **WebRTC**: 实时音视频通信

### AI模型服务
- **数字人生成**: Stable Diffusion + ControlNet
- **对话AI**: GPT-4 / Claude
- **分析AI**: 专业的医疗对话分析模型
- **语音处理**: Whisper + Azure Speech Services

### 部署技术
- **Docker**: 容器化部署
- **Nginx**: 反向代理和负载均衡
- **Electron**: 跨平台桌面应用
- **GitHub Actions**: CI/CD自动化

## 🚀 快速开始

### 📋 环境要求

#### 最小配置
- **操作系统**: Windows 10+ / macOS 10.15+ / Ubuntu 18.04+
- **Python**: 3.9+
- **Node.js**: 16+
- **内存**: 8GB RAM
- **存储**: 20GB 可用空间

#### 推荐配置
- **CPU**: 8核心以上
- **内存**: 16GB RAM以上
- **GPU**: NVIDIA RTX 3060以上（AI模型加速）
- **存储**: 50GB SSD

### 🛠️ 安装步骤

#### 1. 克隆项目
```bash
git clone https://github.com/your-org/virtual-patient-system.git
cd virtual-patient-system
```

#### 2. 后端环境设置
```bash
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接和API密钥
```

#### 3. 前端环境设置
```bash
cd frontend

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 文件，配置API地址
```

#### 4. 数据库设置
```bash
# 使用Docker启动数据库服务
docker-compose -f docker-compose.dev.yml up -d mongodb redis

# 或手动安装MongoDB和Redis
# MongoDB: https://docs.mongodb.com/manual/installation/
# Redis: https://redis.io/download
```

#### 5. 启动服务
```bash
# 启动后端服务
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 新开终端，启动前端服务
cd frontend
npm run dev
```

#### 6. 访问应用
- **前端应用**: http://localhost:3000
- **API文档**: http://localhost:8000/docs
- **API健康检查**: http://localhost:8000/health

### 🎮 使用指南

#### 基本流程
1. **配置系统**: 选择难度级别和科室偏好
2. **生成患者**: 系统自动生成虚拟患者
3. **开始对话**: 与虚拟患者进行问诊
4. **语音交互**: 可选择语音输入和回复
5. **完成评估**: 10轮对话后触发分析
6. **查看报告**: 获得详细的评估报告

#### 评估维度
- **情绪价值 (25%)**: 共情能力、情感支持、沟通温暖度
- **诊断正确性 (30%)**: 诊断准确度、推理过程质量
- **科室分配 (20%)**: 转诊建议的准确性
- **对话质量 (15%)**: 信息收集完整性、沟通技巧
- **总体表现 (10%)**: 综合评分和专业建议

## 📁 项目结构

```
virtual-patient-system/
├── 📁 backend/                 # 后端服务
│   ├── 📁 app/
│   │   ├── 📁 api/            # API路由定义
│   │   ├── 📁 models/         # 数据模型
│   │   ├── 📁 services/       # 业务逻辑服务
│   │   ├── 📁 utils/          # 工具函数
│   │   └── 📄 main.py         # FastAPI应用入口
│   ├── 📄 requirements.txt    # Python依赖
│   └── 📄 .env.example       # 环境变量模板
├── 📁 frontend/               # 前端应用
│   ├── 📁 src/
│   │   ├── 📁 components/     # Vue组件
│   │   ├── 📁 views/          # 页面视图
│   │   ├── 📁 utils/          # 工具函数
│   │   ├── 📁 assets/         # 静态资源
│   │   └── 📄 main.js         # Vue应用入口
│   ├── 📄 package.json       # Node.js依赖
│   └── 📄 vite.config.js     # Vite配置
├── 📁 electron/               # 桌面应用
│   ├── 📄 main.js            # Electron主进程
│   ├── 📄 preload.js         # 预加载脚本
│   └── 📄 package.json       # Electron配置
├── 📁 docs/                   # 项目文档
│   ├── 📄 api-documentation.md
│   ├── 📄 deployment-guide.md
│   ├── 📄 testing-guide.md
│   ├── 📄 ai-models-deployment.md
│   └── 📄 voice-models-deployment.md
├── 📁 tests/                  # 测试文件
├── 📄 docker-compose.yml     # Docker编排
├── 📄 README.md              # 项目说明
└── 📄 LICENSE                # 开源许可证
```

## 🚀 部署

### 🐳 Docker部署（推荐）

#### 开发环境
```bash
# 启动开发环境
docker-compose -f docker-compose.dev.yml up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f backend
```

#### 生产环境
```bash
# 构建生产镜像
docker-compose -f docker-compose.prod.yml build

# 启动生产服务
docker-compose -f docker-compose.prod.yml up -d

# 健康检查
curl -f http://localhost/health
```

### 🖥️ Electron桌面应用

```bash
# 构建桌面应用
cd electron
npm install
npm run build

# 生成安装包
npm run dist
```

详细部署指南请参考 [部署文档](docs/deployment-guide.md)

## 📚 文档

### 📖 用户文档
- [🚀 快速开始指南](docs/quick-start.md)
- [📱 用户使用手册](docs/user-manual.md)
- [❓ 常见问题解答](docs/faq.md)

### 🔧 开发文档
- [🏗️ API接口文档](docs/api-documentation.md)
- [🧪 测试指南](docs/testing-guide.md)
- [🚀 部署指南](docs/deployment-guide.md)

### 🤖 AI模型文档
- [🎭 AI模型部署指南](docs/ai-models-deployment.md)
- [🎤 语音模型部署指南](docs/voice-models-deployment.md)
- [📊 模型性能优化](docs/model-optimization.md)

## 🧪 测试

### 运行测试
```bash
# 后端测试
cd backend
pytest -v --cov=app

# 前端测试
cd frontend
npm run test

# E2E测试
npm run test:e2e
```

### 测试覆盖率
- **后端**: 目标 >80%
- **前端**: 目标 >70%
- **集成测试**: 覆盖主要用户流程

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 🐛 报告问题
1. 搜索现有的 [Issues](https://github.com/your-org/virtual-patient-system/issues)
2. 使用问题模板创建新的Issue
3. 提供详细的复现步骤和环境信息

### 💡 功能建议
1. 在 [Discussions](https://github.com/your-org/virtual-patient-system/discussions) 中讨论想法
2. 创建功能请求Issue
3. 参与社区讨论

### 🔧 代码贡献
1. Fork 项目到您的GitHub账户
2. 创建功能分支: `git checkout -b feature/amazing-feature`
3. 遵循代码规范和测试要求
4. 提交更改: `git commit -m 'Add amazing feature'`
5. 推送分支: `git push origin feature/amazing-feature`
6. 创建Pull Request

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE) - 查看LICENSE文件了解详情。

## 🙏 致谢

### 核心贡献者
- **项目负责人**: [您的姓名](https://github.com/yourusername)
- **AI模型专家**: [专家姓名](https://github.com/expert)
- **医学顾问**: [医生姓名](https://github.com/doctor)

### 技术支持
- [OpenAI](https://openai.com/) - GPT-4 API
- [Microsoft Azure](https://azure.microsoft.com/) - 语音服务
- [MongoDB](https://www.mongodb.com/) - 数据库支持
- [Vue.js](https://vuejs.org/) - 前端框架

## 📞 支持与联系

### 🆘 获取帮助
1. 📖 查看 [文档](docs/)
2. 🔍 搜索 [Issues](https://github.com/your-org/virtual-patient-system/issues)
3. 💬 参与 [Discussions](https://github.com/your-org/virtual-patient-system/discussions)
4. 📧 发送邮件至 <EMAIL>

### ⚠️ 重要声明

**本系统仅用于教育和训练目的，不应用于实际的医疗诊断或治疗决策。所有医疗决策应由合格的医疗专业人员做出。**

---

<div align="center">

**如果这个项目对您有帮助，请给我们一个 ⭐️**

Made with ❤️ by [Your Company](https://yourcompany.com)

</div>
