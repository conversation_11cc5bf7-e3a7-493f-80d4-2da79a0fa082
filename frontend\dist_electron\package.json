{"name": "emotion-analysis-assistant", "version": "0.1.0", "private": true, "description": "情绪分析助手 - 跨平台Electron应用", "author": "", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "electron:serve": "vue-cli-service electron:serve", "electron:build": "vue-cli-service electron:build", "postinstall": "electron-builder install-app-deps", "postuninstall": "electron-builder install-app-deps"}, "main": "background.js", "dependencies": {"axios": "^0.21.4", "core-js": "^3.6.5", "element-plus": "^2.3.8", "vue": "^3.0.0", "vue-router": "^4.0.0-0", "vuex": "^4.0.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "babel-eslint": "^10.1.0", "electron": "^13.0.0", "electron-devtools-installer": "^3.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "vue-cli-plugin-electron-builder": "~2.1.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}