"""
完整工作流程集成测试

测试从患者生成到分析报告导出的完整流程
"""

import pytest
import asyncio
from httpx import AsyncClient
from datetime import datetime
import json

from app.main import app

class TestCompleteWorkflow:
    """完整工作流程测试"""
    
    @pytest.mark.asyncio
    async def test_complete_patient_conversation_workflow(self):
        """测试完整的患者对话工作流程"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            
            # 1. 生成虚拟患者
            print("步骤1: 生成虚拟患者")
            patient_response = await client.post("/api/virtual-patient/generate", json={
                "difficulty_level": 2,
                "department_preference": "cardiology"
            })
            
            assert patient_response.status_code == 200
            patient_data = patient_response.json()
            assert patient_data["success"] is True
            
            session_id = patient_data["session_id"]
            patient_id = patient_data["patient"]["id"]
            
            print(f"✓ 患者生成成功，会话ID: {session_id}")
            
            # 2. 开始对话会话
            print("步骤2: 开始对话会话")
            session_response = await client.post("/api/conversation/start", json={
                "session_id": session_id,
                "patient_id": patient_id
            })
            
            assert session_response.status_code == 200
            session_data = session_response.json()
            assert session_data["success"] is True
            
            print("✓ 对话会话启动成功")
            
            # 3. 进行多轮对话
            print("步骤3: 进行多轮对话")
            conversation_rounds = [
                "您好，请问您哪里不舒服？",
                "请详细描述一下您的症状。",
                "这种症状持续多长时间了？",
                "有什么诱发因素吗？",
                "您之前有过类似的情况吗？",
                "您有什么既往病史吗？",
                "您目前在服用什么药物吗？",
                "您有药物过敏史吗？",
                "根据您的症状，我初步考虑可能是心绞痛。",
                "建议您到心内科进一步检查。"
            ]
            
            for i, doctor_message in enumerate(conversation_rounds, 1):
                print(f"  第{i}轮对话...")
                
                message_response = await client.post("/api/conversation/message", json={
                    "session_id": session_id,
                    "message": doctor_message,
                    "message_type": "text"
                })
                
                assert message_response.status_code == 200
                message_data = message_response.json()
                assert message_data["success"] is True
                assert "patient_response" in message_data
                
                print(f"    医生: {doctor_message}")
                print(f"    患者: {message_data['patient_response']}")
                
                # 模拟思考时间
                await asyncio.sleep(0.1)
            
            print("✓ 对话完成")
            
            # 4. 触发高级分析
            print("步骤4: 触发高级分析")
            analysis_response = await client.post(f"/api/conversation-analysis/advanced/{session_id}")
            
            assert analysis_response.status_code == 200
            analysis_data = analysis_response.json()
            assert analysis_data["success"] is True
            
            analysis_result = analysis_data["analysis_result"]
            assert "overall_score" in analysis_result
            assert "dimensions" in analysis_result
            
            print(f"✓ 分析完成，总分: {analysis_result['overall_score']:.1f}")
            
            # 5. 验证分析结果的完整性
            print("步骤5: 验证分析结果")
            
            # 检查五个维度
            expected_dimensions = [
                "emotional_value",
                "diagnosis_accuracy", 
                "department_accuracy",
                "conversation_quality",
                "overall_assessment"
            ]
            
            for dimension in expected_dimensions:
                assert dimension in analysis_result["dimensions"]
                dim_data = analysis_result["dimensions"][dimension]
                assert "score" in dim_data
                assert "detailed_feedback" in dim_data
                assert "recommendations" in dim_data
                
                print(f"  ✓ {dim_data['name']}: {dim_data['score']:.1f}分")
            
            # 6. 生成PDF报告
            print("步骤6: 生成PDF报告")
            pdf_response = await client.post(f"/api/pdf/generate/{session_id}")
            
            assert pdf_response.status_code == 200
            pdf_data = pdf_response.json()
            assert pdf_data["success"] is True
            
            pdf_info = pdf_data["pdf_info"]
            assert "pdf_filename" in pdf_info
            assert "download_url" in pdf_info
            
            print(f"✓ PDF生成成功: {pdf_info['pdf_filename']}")
            
            # 7. 获取分析历史
            print("步骤7: 验证数据持久化")
            history_response = await client.get("/api/conversation-analysis/history")
            
            assert history_response.status_code == 200
            history_data = history_response.json()
            assert history_data["success"] is True
            assert len(history_data["history"]) > 0
            
            # 验证我们的会话在历史中
            session_found = False
            for record in history_data["history"]:
                if record["session_id"] == session_id:
                    session_found = True
                    break
            
            assert session_found, "会话记录未在历史中找到"
            print("✓ 数据持久化验证成功")
            
            # 8. 清理测试数据
            print("步骤8: 清理测试数据")
            cleanup_response = await client.delete(f"/api/conversation-analysis/result/{session_id}")
            
            assert cleanup_response.status_code == 200
            cleanup_data = cleanup_response.json()
            assert cleanup_data["success"] is True
            
            print("✓ 测试数据清理完成")
            
            print("\n🎉 完整工作流程测试通过！")
            
            return {
                "session_id": session_id,
                "patient_id": patient_id,
                "conversation_rounds": len(conversation_rounds),
                "overall_score": analysis_result["overall_score"],
                "pdf_filename": pdf_info["pdf_filename"]
            }
    
    @pytest.mark.asyncio
    async def test_error_handling_workflow(self):
        """测试错误处理工作流程"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            
            # 测试无效会话ID的分析
            print("测试错误处理: 无效会话ID")
            invalid_session_id = "invalid-session-id"
            
            analysis_response = await client.post(f"/api/conversation-analysis/advanced/{invalid_session_id}")
            assert analysis_response.status_code == 500  # 应该返回错误
            
            # 测试无效患者ID的对话
            print("测试错误处理: 无效患者ID")
            session_response = await client.post("/api/conversation/start", json={
                "session_id": "test-session",
                "patient_id": "invalid-patient-id"
            })
            assert session_response.status_code == 500  # 应该返回错误
            
            print("✓ 错误处理测试通过")
    
    @pytest.mark.asyncio
    async def test_performance_workflow(self):
        """测试性能工作流程"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            
            print("性能测试: 并发患者生成")
            start_time = datetime.now()
            
            # 并发生成5个患者
            tasks = []
            for i in range(5):
                task = client.post("/api/virtual-patient/generate", json={
                    "difficulty_level": 1
                })
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 验证所有请求都成功
            for response in responses:
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
            
            print(f"✓ 并发生成5个患者耗时: {duration:.2f}秒")
            
            # 性能要求：5个并发请求应在10秒内完成
            assert duration < 10.0, f"性能测试失败，耗时过长: {duration:.2f}秒"
            
            print("✓ 性能测试通过")
    
    @pytest.mark.asyncio
    async def test_data_consistency_workflow(self):
        """测试数据一致性工作流程"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            
            print("数据一致性测试")
            
            # 生成患者
            patient_response = await client.post("/api/virtual-patient/generate", json={
                "difficulty_level": 1
            })
            
            assert patient_response.status_code == 200
            patient_data = patient_response.json()
            session_id = patient_data["session_id"]
            
            # 开始对话
            session_response = await client.post("/api/conversation/start", json={
                "session_id": session_id,
                "patient_id": patient_data["patient"]["id"]
            })
            
            assert session_response.status_code == 200
            
            # 发送消息
            message_response = await client.post("/api/conversation/message", json={
                "session_id": session_id,
                "message": "测试消息",
                "message_type": "text"
            })
            
            assert message_response.status_code == 200
            
            # 获取对话历史
            history_response = await client.get(f"/api/conversation/history/{session_id}")
            
            assert history_response.status_code == 200
            history_data = history_response.json()
            
            # 验证数据一致性
            assert len(history_data["messages"]) >= 2  # 至少有医生和患者的消息
            
            # 验证消息顺序
            messages = history_data["messages"]
            doctor_message = next((msg for msg in messages if msg["role"] == "doctor"), None)
            patient_message = next((msg for msg in messages if msg["role"] == "patient"), None)
            
            assert doctor_message is not None, "未找到医生消息"
            assert patient_message is not None, "未找到患者回复"
            assert doctor_message["content"] == "测试消息"
            
            print("✓ 数据一致性测试通过")
            
            # 清理
            await client.delete(f"/api/conversation-analysis/result/{session_id}")

if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-s"])
