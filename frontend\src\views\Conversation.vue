<template>
  <div class="conversation-container">
    <!-- 对话主体区域 -->
    <div class="conversation-main">
      <!-- 左侧：人物窗口 (2/3 宽度) -->
      <div class="avatar-panel">
        <div class="patient-info-header">
          <h3 class="patient-name">{{ currentPatient.name }}</h3>
          <div class="patient-basic-info">
            <span class="patient-age">{{ currentPatient.age }}岁</span>
            <span class="patient-gender">{{ getGenderLabel(currentPatient.gender) }}</span>
            <span class="patient-complaint">{{ currentPatient.chief_complaint }}</span>
          </div>
        </div>
        
        <!-- 数字人展示区域 -->
        <div class="avatar-display">
          <div class="avatar-container">
            <img 
              v-if="currentPatient.avatar_url" 
              :src="currentPatient.avatar_url" 
              :alt="currentPatient.name"
              class="patient-avatar"
            />
            <div v-else class="avatar-placeholder">
              <i class="icon-user-large"></i>
              <p>数字人加载中...</p>
            </div>
            
            <!-- 语音状态指示器 -->
            <div v-if="isPatientSpeaking" class="speaking-indicator">
              <div class="wave-animation">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 患者状态信息 -->
        <div class="patient-status">
          <div class="status-item">
            <span class="status-label">情绪状态:</span>
            <span class="status-value" :class="getEmotionClass(currentPatient.emotional_state)">
              {{ getEmotionLabel(currentPatient.emotional_state) }}
            </span>
          </div>
          <div class="status-item">
            <span class="status-label">配合程度:</span>
            <span class="status-value">{{ currentPatient.cooperation_level }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧：聊天记录窗口 (1/3 宽度) -->
      <div class="chat-panel">
        <!-- 对话进度 -->
        <div class="conversation-progress">
          <div class="progress-header">
            <span class="round-info">第 {{ currentRound }}/{{ maxRounds }} 轮</span>
            <button 
              class="btn btn-small btn-warning"
              @click="submitAnalysis"
              :disabled="currentRound < 3"
            >
              提交分析
            </button>
          </div>
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              :style="{ width: (currentRound / maxRounds * 100) + '%' }"
            ></div>
          </div>
        </div>

        <!-- 聊天记录 -->
        <div class="chat-messages" ref="chatMessages">
          <div 
            v-for="message in conversationHistory" 
            :key="message.id"
            class="message-item"
            :class="{ 
              'doctor-message': message.role === 'doctor',
              'patient-message': message.role === 'patient'
            }"
          >
            <div class="message-avatar">
              <i :class="message.role === 'doctor' ? 'icon-doctor' : 'icon-patient'"></i>
            </div>
            <div class="message-content">
              <div class="message-header">
                <span class="message-sender">
                  {{ message.role === 'doctor' ? '医生' : currentPatient.name }}
                </span>
                <span class="message-time">{{ formatTime(message.timestamp) }}</span>
              </div>
              <div class="message-text">{{ message.content }}</div>
              <div v-if="message.audio_url" class="message-audio">
                <audio controls>
                  <source :src="message.audio_url" type="audio/wav">
                </audio>
              </div>
            </div>
          </div>
          
          <!-- 患者正在输入指示器 -->
          <div v-if="isPatientTyping" class="typing-indicator">
            <div class="typing-avatar">
              <i class="icon-patient"></i>
            </div>
            <div class="typing-content">
              <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input-area">
          <div class="input-controls">
            <!-- 语音录制组件 -->
            <VoiceRecorder
              :session-id="sessionId"
              :auto-send="true"
              :max-duration="60"
              @transcription="handleVoiceTranscription"
              @error="handleVoiceError"
              @recording-start="handleRecordingStart"
              @recording-stop="handleRecordingStop"
            />

            <textarea
              v-model="currentMessage"
              class="message-input"
              placeholder="请输入您的问题或建议..."
              rows="3"
              @keydown.enter.exact="sendMessage"
              @keydown.enter.shift.exact.prevent="currentMessage += '\n'"
              :disabled="isWaitingResponse"
            ></textarea>

            <button
              class="btn btn-primary send-btn"
              @click="sendMessage"
              :disabled="!canSendMessage"
            >
              <i class="icon-send"></i>
            </button>
          </div>
          
          <!-- 建议问题 -->
          <div v-if="suggestedQuestions.length > 0" class="suggested-questions">
            <div class="suggestions-header">建议问题:</div>
            <div class="suggestions-list">
              <button 
                v-for="suggestion in suggestedQuestions" 
                :key="suggestion"
                class="suggestion-btn"
                @click="useSuggestion(suggestion)"
              >
                {{ suggestion }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VoiceRecorder from '../components/VoiceRecorder.vue';

export default {
  name: 'ConversationView',
  components: {
    VoiceRecorder
  },
  data() {
    return {
      currentPatient: {},
      sessionId: '',
      currentRound: 0,
      maxRounds: 10,
      conversationHistory: [],
      currentMessage: '',
      isPatientTyping: false,
      isPatientSpeaking: false,
      isWaitingResponse: false,
      suggestedQuestions: [
        '您好，请问您哪里不舒服？',
        '这种症状持续多长时间了？',
        '您之前有过类似的情况吗？',
        '您目前在服用什么药物吗？'
      ]
    };
  },
  computed: {
    canSendMessage() {
      return this.currentMessage.trim() && !this.isWaitingResponse;
    }
  },
  mounted() {
    this.initializeConversation();
  },
  methods: {
    async initializeConversation() {
      // 从store或路由参数获取患者信息
      this.currentPatient = this.$store.state.currentPatient || {};
      this.sessionId = this.$store.state.sessionId || '';
      
      if (!this.sessionId) {
        this.$message.error('会话信息丢失，请重新开始');
        this.$router.push('/');
        return;
      }
      
      // 开始对话会话
      try {
        await this.$http.post('/api/conversation/start', {
          session_id: this.sessionId,
          patient_id: this.currentPatient.id
        });
      } catch (error) {
        console.error('初始化对话失败:', error);
        this.$message.error('初始化对话失败');
      }
    },
    
    async sendMessage() {
      if (!this.canSendMessage) return;
      
      const message = this.currentMessage.trim();
      this.currentMessage = '';
      this.isWaitingResponse = true;
      
      // 添加医生消息到历史记录
      this.conversationHistory.push({
        id: Date.now(),
        role: 'doctor',
        content: message,
        timestamp: new Date()
      });
      
      this.scrollToBottom();
      this.isPatientTyping = true;
      
      try {
        // 发送消息到后端
        const response = await this.$http.post('/api/conversation/message', {
          session_id: this.sessionId,
          message: message,
          message_type: 'text'
        });
        
        if (response.data.success) {
          // 添加患者回复
          this.conversationHistory.push({
            id: Date.now() + 1,
            role: 'patient',
            content: response.data.patient_response,
            audio_url: response.data.audio_url,
            timestamp: new Date()
          });
          
          this.currentRound = response.data.round_number;
          
          // 检查是否对话完成
          if (response.data.is_conversation_complete) {
            this.handleConversationComplete();
          }
        }
      } catch (error) {
        console.error('发送消息失败:', error);
        this.$message.error('发送消息失败，请重试');
      } finally {
        this.isPatientTyping = false;
        this.isWaitingResponse = false;
        this.scrollToBottom();
      }
    },
    
    async submitAnalysis() {
      try {
        this.$message.info('正在分析对话内容...');
        
        const response = await this.$http.post(`/api/analysis/trigger/${this.sessionId}`);
        
        if (response.data.success) {
          // 保存分析结果并跳转到结果页面
          this.$store.commit('setAnalysisResult', response.data.result);
          this.$router.push('/result');
        } else {
          this.$message.error('分析失败，请重试');
        }
      } catch (error) {
        console.error('提交分析失败:', error);
        this.$message.error('分析失败，请重试');
      }
    },
    
    handleConversationComplete() {
      this.$message.success('对话已完成，正在进行分析...');
      setTimeout(() => {
        this.submitAnalysis();
      }, 2000);
    },
    
    // 语音转录处理
    handleVoiceTranscription(transcription) {
      console.log('语音转录结果:', transcription);

      // 将转录结果填入输入框
      this.currentMessage = transcription.text;

      // 如果转录成功且有内容，自动发送消息
      if (transcription.text.trim()) {
        this.$nextTick(() => {
          this.sendMessage();
        });
      }
    },

    // 语音错误处理
    handleVoiceError(error) {
      console.error('语音处理错误:', error);
      this.$message.error('语音处理失败: ' + (error.message || '未知错误'));
    },

    // 录音开始处理
    handleRecordingStart() {
      console.log('开始录音');
      this.isPatientSpeaking = false; // 停止患者语音播放
    },

    // 录音停止处理
    handleRecordingStop(audioData) {
      console.log('录音停止，音频数据:', audioData);
    },
    
    useSuggestion(suggestion) {
      this.currentMessage = suggestion;
    },
    
    getGenderLabel(gender) {
      const labels = {
        'male': '男',
        'female': '女',
        'other': '其他'
      };
      return labels[gender] || '未知';
    },
    
    getEmotionLabel(emotion) {
      const labels = {
        'anxious': '焦虑',
        'calm': '平静',
        'worried': '担心',
        'cooperative': '配合',
        'neutral': '中性'
      };
      return labels[emotion] || emotion;
    },
    
    getEmotionClass(emotion) {
      return `emotion-${emotion}`;
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    
    scrollToBottom() {
      this.$nextTick(() => {
        const chatMessages = this.$refs.chatMessages;
        if (chatMessages) {
          chatMessages.scrollTop = chatMessages.scrollHeight;
        }
      });
    }
  }
};
</script>

<style scoped>
.conversation-container {
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.conversation-main {
  display: flex;
  height: 100vh;
}

/* 左侧人物窗口 (2/3 宽度) */
.avatar-panel {
  flex: 2;
  background: white;
  border-right: 1px solid #e1e8ed;
  display: flex;
  flex-direction: column;
}

.patient-info-header {
  padding: 20px;
  border-bottom: 1px solid #e1e8ed;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.patient-name {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 10px;
}

.patient-basic-info {
  display: flex;
  gap: 15px;
  font-size: 14px;
  opacity: 0.9;
}

.patient-basic-info span {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
}

.avatar-display {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.avatar-container {
  position: relative;
  width: 300px;
  height: 400px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  background: white;
}

.patient-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6c757d;
}

.avatar-placeholder .icon-user-large {
  font-size: 80px;
  margin-bottom: 20px;
}

.speaking-indicator {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(52, 152, 219, 0.9);
  padding: 10px 20px;
  border-radius: 20px;
  color: white;
}

.wave-animation {
  display: flex;
  gap: 4px;
  align-items: center;
}

.wave-animation span {
  width: 4px;
  height: 20px;
  background: white;
  border-radius: 2px;
  animation: wave 1.5s ease-in-out infinite;
}

.wave-animation span:nth-child(2) {
  animation-delay: 0.2s;
}

.wave-animation span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes wave {
  0%, 100% { transform: scaleY(1); }
  50% { transform: scaleY(1.5); }
}

.patient-status {
  padding: 20px;
  border-top: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.status-label {
  font-weight: 600;
  color: #495057;
}

.status-value {
  font-weight: 500;
}

.emotion-anxious { color: #e74c3c; }
.emotion-calm { color: #27ae60; }
.emotion-worried { color: #f39c12; }
.emotion-cooperative { color: #3498db; }
.emotion-neutral { color: #6c757d; }

/* 右侧聊天窗口 (1/3 宽度) */
.chat-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.conversation-progress {
  padding: 15px 20px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.round-info {
  font-weight: 600;
  color: #495057;
}

.progress-bar {
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  transition: width 0.3s ease;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f8f9fa;
}

.message-item {
  display: flex;
  margin-bottom: 20px;
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
}

.doctor-message .message-avatar {
  background: #3498db;
  color: white;
}

.patient-message .message-avatar {
  background: #e74c3c;
  color: white;
}

.message-content {
  flex: 1;
  max-width: calc(100% - 52px);
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.message-sender {
  font-weight: 600;
  font-size: 14px;
  color: #495057;
}

.message-time {
  font-size: 12px;
  color: #6c757d;
}

.message-text {
  background: white;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  line-height: 1.5;
}

.doctor-message .message-text {
  background: #3498db;
  color: white;
}

.message-audio {
  margin-top: 8px;
}

.message-audio audio {
  width: 100%;
  max-width: 300px;
}

.typing-indicator {
  display: flex;
  margin-bottom: 20px;
  opacity: 0.7;
}

.typing-content {
  background: white;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  background: #6c757d;
  border-radius: 50%;
  animation: typing 1.5s ease-in-out infinite;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-10px); }
}

.chat-input-area {
  border-top: 1px solid #e1e8ed;
  background: white;
  padding: 20px;
}

.input-controls {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.btn-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-icon:hover {
  background: #e9ecef;
  color: #495057;
}

.btn-icon.active {
  background: #e74c3c;
  color: white;
  border-color: #e74c3c;
}

.message-input {
  flex: 1;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 12px 16px;
  resize: none;
  outline: none;
  font-family: inherit;
  transition: border-color 0.3s ease;
}

.message-input:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.send-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #3498db;
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.send-btn:hover:not(:disabled) {
  background: #2980b9;
  transform: scale(1.05);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.suggested-questions {
  margin-top: 15px;
}

.suggestions-header {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 8px;
}

.suggestions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 12px;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
}

.suggestion-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

/* 按钮通用样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-small {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-warning {
  background: #f39c12;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #e67e22;
}

/* 图标 */
.icon-doctor::before { content: '👨‍⚕️'; }
.icon-patient::before { content: '🤒'; }
.icon-user-large::before { content: '👤'; }
.icon-mic::before { content: '🎤'; }
.icon-mic-active::before { content: '🔴'; }
.icon-send::before { content: '📤'; }

/* 响应式设计 */
@media (max-width: 768px) {
  .conversation-main {
    flex-direction: column;
  }

  .avatar-panel {
    flex: 1;
    max-height: 50vh;
  }

  .chat-panel {
    flex: 1;
  }

  .avatar-container {
    width: 200px;
    height: 250px;
  }
}
</style>
