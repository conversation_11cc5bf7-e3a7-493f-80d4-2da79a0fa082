{"name": "llm_electron", "version": "1.0.0", "description": "## 项目概述 这是一个基于Electron的跨平台应用，用于情绪分析和记录。前端使用Vue.js框架，后端采用FastAPI，数据存储在MongoDB中。", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "cd frontend && npm run electron:serve", "electron:serve": "cd frontend && npm run electron:serve", "electron:build": "cd frontend && npm run electron:build", "install-all": "npm install && cd frontend && npm install && cd ../backend && pip install -r requirements.txt"}, "keywords": [], "author": "", "license": "ISC"}