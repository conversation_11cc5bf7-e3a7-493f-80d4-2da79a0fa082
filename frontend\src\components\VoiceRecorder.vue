<template>
  <div class="voice-recorder">
    <!-- 录音按钮 -->
    <button 
      class="record-btn"
      :class="{ 
        'recording': isRecording, 
        'disabled': !isSupported || isProcessing 
      }"
      @click="toggleRecording"
      :disabled="!isSupported || isProcessing"
      :title="getButtonTitle()"
    >
      <div class="btn-content">
        <i :class="getButtonIcon()"></i>
        <span class="btn-text">{{ getButtonText() }}</span>
      </div>
      
      <!-- 录音动画 -->
      <div v-if="isRecording" class="recording-animation">
        <div class="wave-circle"></div>
        <div class="wave-circle"></div>
        <div class="wave-circle"></div>
      </div>
    </button>

    <!-- 录音状态显示 -->
    <div v-if="isRecording" class="recording-status">
      <div class="recording-timer">{{ formatTime(recordingTime) }}</div>
      <div class="recording-level">
        <div class="level-bar" :style="{ width: audioLevel + '%' }"></div>
      </div>
    </div>

    <!-- 处理状态 -->
    <div v-if="isProcessing" class="processing-status">
      <div class="processing-spinner"></div>
      <span class="processing-text">{{ processingText }}</span>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-message">
      <i class="icon-error"></i>
      <span>{{ error }}</span>
      <button class="error-close" @click="clearError">×</button>
    </div>

    <!-- 浏览器不支持提示 -->
    <div v-if="!isSupported" class="unsupported-message">
      <i class="icon-warning"></i>
      <span>您的浏览器不支持语音录制功能</span>
    </div>
  </div>
</template>

<script>
import { audioUtils, voiceService } from '../utils/audioUtils.js';

export default {
  name: 'VoiceRecorder',
  props: {
    sessionId: {
      type: String,
      required: true
    },
    autoSend: {
      type: Boolean,
      default: true
    },
    maxDuration: {
      type: Number,
      default: 60 // 最大录制时长（秒）
    }
  },
  emits: ['transcription', 'error', 'recording-start', 'recording-stop'],
  data() {
    return {
      isRecording: false,
      isProcessing: false,
      isSupported: false,
      recordingTime: 0,
      audioLevel: 0,
      error: null,
      processingText: '正在处理...',
      recordingTimer: null,
      levelTimer: null
    };
  },
  mounted() {
    this.checkSupport();
    this.initializeAudio();
  },
  beforeUnmount() {
    this.cleanup();
  },
  methods: {
    /**
     * 检查浏览器支持
     */
    checkSupport() {
      const support = audioUtils.constructor.checkBrowserSupport();
      this.isSupported = support.isSupported;
      
      if (!this.isSupported) {
        console.warn('浏览器不支持语音录制:', support.details);
      }
    },

    /**
     * 初始化音频
     */
    async initializeAudio() {
      try {
        await audioUtils.initAudioContext();
      } catch (error) {
        console.error('音频初始化失败:', error);
        this.error = '音频初始化失败';
      }
    },

    /**
     * 切换录音状态
     */
    async toggleRecording() {
      if (this.isRecording) {
        await this.stopRecording();
      } else {
        await this.startRecording();
      }
    },

    /**
     * 开始录音
     */
    async startRecording() {
      try {
        this.clearError();
        this.isRecording = true;
        this.recordingTime = 0;
        
        // 开始录音
        await audioUtils.startRecording();
        
        // 启动计时器
        this.startTimer();
        
        // 启动音量监测
        this.startLevelMonitoring();
        
        // 发出事件
        this.$emit('recording-start');
        
        console.log('开始录音');
      } catch (error) {
        console.error('开始录音失败:', error);
        this.isRecording = false;
        this.error = error.message || '开始录音失败';
        this.$emit('error', error);
      }
    },

    /**
     * 停止录音
     */
    async stopRecording() {
      try {
        this.isRecording = false;
        this.isProcessing = true;
        this.processingText = '正在处理录音...';
        
        // 停止计时器
        this.stopTimer();
        this.stopLevelMonitoring();
        
        // 停止录音并获取音频数据
        const audioData = await audioUtils.stopRecording();
        
        // 发出事件
        this.$emit('recording-stop', audioData);
        
        // 如果启用自动发送，进行语音识别
        if (this.autoSend) {
          await this.processAudio(audioData);
        }
        
        console.log('录音完成');
      } catch (error) {
        console.error('停止录音失败:', error);
        this.error = error.message || '停止录音失败';
        this.$emit('error', error);
      } finally {
        this.isProcessing = false;
      }
    },

    /**
     * 处理音频数据
     */
    async processAudio(audioData) {
      try {
        this.processingText = '正在识别语音...';
        
        // 调用语音转文字API
        const result = await voiceService.speechToText(
          audioData.base64,
          this.sessionId,
          'zh-CN',
          'wav'
        );
        
        if (result.text) {
          // 发出转录结果
          this.$emit('transcription', {
            text: result.text,
            confidence: result.confidence,
            duration: result.duration
          });
        } else {
          throw new Error('语音识别失败');
        }
      } catch (error) {
        console.error('语音处理失败:', error);
        this.error = '语音识别失败，请重试';
        this.$emit('error', error);
      }
    },

    /**
     * 启动录音计时器
     */
    startTimer() {
      this.recordingTimer = setInterval(() => {
        this.recordingTime++;
        
        // 检查是否超过最大时长
        if (this.recordingTime >= this.maxDuration) {
          this.stopRecording();
        }
      }, 1000);
    },

    /**
     * 停止录音计时器
     */
    stopTimer() {
      if (this.recordingTimer) {
        clearInterval(this.recordingTimer);
        this.recordingTimer = null;
      }
    },

    /**
     * 启动音量监测
     */
    startLevelMonitoring() {
      // 简化的音量监测，实际项目中可以使用Web Audio API
      this.levelTimer = setInterval(() => {
        this.audioLevel = Math.random() * 100; // 模拟音量
      }, 100);
    },

    /**
     * 停止音量监测
     */
    stopLevelMonitoring() {
      if (this.levelTimer) {
        clearInterval(this.levelTimer);
        this.levelTimer = null;
        this.audioLevel = 0;
      }
    },

    /**
     * 清理资源
     */
    cleanup() {
      this.stopTimer();
      this.stopLevelMonitoring();
      audioUtils.cleanup();
    },

    /**
     * 清除错误
     */
    clearError() {
      this.error = null;
    },

    /**
     * 格式化时间显示
     */
    formatTime(seconds) {
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    },

    /**
     * 获取按钮图标
     */
    getButtonIcon() {
      if (this.isProcessing) return 'icon-loading';
      if (this.isRecording) return 'icon-stop';
      return 'icon-mic';
    },

    /**
     * 获取按钮文本
     */
    getButtonText() {
      if (this.isProcessing) return '处理中...';
      if (this.isRecording) return '停止录音';
      return '开始录音';
    },

    /**
     * 获取按钮提示
     */
    getButtonTitle() {
      if (!this.isSupported) return '浏览器不支持录音功能';
      if (this.isProcessing) return '正在处理录音';
      if (this.isRecording) return '点击停止录音';
      return '点击开始录音';
    }
  }
};
</script>

<style scoped>
.voice-recorder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.record-btn {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.record-btn:hover:not(.disabled) {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

.record-btn.recording {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  animation: pulse 2s infinite;
}

.record-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  z-index: 2;
}

.btn-text {
  font-size: 10px;
  font-weight: 500;
}

.recording-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.wave-circle {
  position: absolute;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: wave 2s infinite;
}

.wave-circle:nth-child(1) {
  width: 80px;
  height: 80px;
  margin: -40px 0 0 -40px;
}

.wave-circle:nth-child(2) {
  width: 100px;
  height: 100px;
  margin: -50px 0 0 -50px;
  animation-delay: 0.5s;
}

.wave-circle:nth-child(3) {
  width: 120px;
  height: 120px;
  margin: -60px 0 0 -60px;
  animation-delay: 1s;
}

@keyframes wave {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.recording-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: rgba(231, 76, 60, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(231, 76, 60, 0.2);
}

.recording-timer {
  font-size: 18px;
  font-weight: 600;
  color: #e74c3c;
  font-family: 'Courier New', monospace;
}

.recording-level {
  width: 100px;
  height: 4px;
  background: #ecf0f1;
  border-radius: 2px;
  overflow: hidden;
}

.level-bar {
  height: 100%;
  background: linear-gradient(90deg, #27ae60, #f39c12, #e74c3c);
  transition: width 0.1s ease;
}

.processing-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(52, 152, 219, 0.2);
}

.processing-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #3498db;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.processing-text {
  font-size: 14px;
  color: #3498db;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(231, 76, 60, 0.1);
  border: 1px solid rgba(231, 76, 60, 0.2);
  border-radius: 8px;
  color: #e74c3c;
  font-size: 14px;
}

.error-close {
  background: none;
  border: none;
  color: #e74c3c;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  margin-left: 8px;
}

.unsupported-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(243, 156, 18, 0.1);
  border: 1px solid rgba(243, 156, 18, 0.2);
  border-radius: 8px;
  color: #f39c12;
  font-size: 14px;
}

/* 图标 */
.icon-mic::before { content: '🎤'; }
.icon-stop::before { content: '⏹️'; }
.icon-loading::before { 
  content: '⏳'; 
  animation: spin 1s linear infinite;
}
.icon-error::before { content: '❌'; }
.icon-warning::before { content: '⚠️'; }
</style>
