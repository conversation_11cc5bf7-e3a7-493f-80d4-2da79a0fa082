"""
PDF服务模块

实现分析结果的PDF导出和打印功能，包括：
1. PDF报告生成
2. 专业模板设计
3. 图表生成
4. 文件管理
"""

import logging
import os
import tempfile
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import uuid

from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from reportlab.pdfgen import canvas
from reportlab.graphics.shapes import Drawing
from reportlab.graphics.charts.piecharts import Pie
from reportlab.graphics.charts.barcharts import VerticalBarChart

from ..models.analysis import VirtualPatientAnalysisResult, PDFExportResponse
from .database import get_database

logger = logging.getLogger(__name__)

class PDFService:
    """PDF服务类"""
    
    def __init__(self):
        self.pdf_storage_path = "/app/storage/pdf"
        self.template_path = "/app/templates/pdf"
        
        # 确保存储目录存在
        os.makedirs(self.pdf_storage_path, exist_ok=True)
        os.makedirs(self.template_path, exist_ok=True)
    
    async def generate_analysis_pdf(
        self,
        session_id: str,
        analysis_result: Dict[str, Any],
        template_type: str = "standard",
        include_charts: bool = True
    ) -> Dict[str, Any]:
        """
        生成分析结果PDF报告
        
        Args:
            session_id: 会话ID
            analysis_result: 分析结果
            template_type: 模板类型
            include_charts: 是否包含图表
            
        Returns:
            PDFExportResponse: PDF导出响应
        """
        try:
            start_time = datetime.now()
            
            # 生成PDF文件名
            pdf_filename = f"analysis_report_{session_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            pdf_path = os.path.join(self.pdf_storage_path, pdf_filename)
            
            # 创建PDF文档
            doc = SimpleDocTemplate(
                pdf_path,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # 构建PDF内容
            story = []
            styles = getSampleStyleSheet()
            
            # 添加标题
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=24,
                spaceAfter=30,
                alignment=TA_CENTER,
                textColor=colors.darkblue
            )
            story.append(Paragraph("虚拟患者对话分析报告", title_style))
            story.append(Spacer(1, 20))
            
            # 添加基本信息
            story.extend(self._add_basic_info(analysis_result, styles))
            
            # 添加分析结果
            story.extend(self._add_analysis_results(analysis_result, styles, include_charts))
            
            # 添加对话记录
            story.extend(self._add_conversation_display(analysis_result, styles))
            
            # 添加建议和总结
            story.extend(self._add_recommendations(analysis_result, styles))
            
            # 生成PDF
            doc.build(story)
            
            # 计算文件大小
            file_size = os.path.getsize(pdf_path)
            
            # 计算生成时间
            generation_time = (datetime.now() - start_time).total_seconds()
            
            # 设置过期时间（7天后）
            expires_at = datetime.now() + timedelta(days=7)
            
            # 生成访问URL
            pdf_url = f"/api/analysis/export/pdf/{session_id}"
            
            # 保存PDF信息到数据库
            await self._save_pdf_info(session_id, pdf_filename, pdf_path, expires_at)
            
            logger.info(f"PDF报告生成成功: {pdf_filename}")
            
            return PDFExportResponse(
                pdf_url=pdf_url,
                file_size=file_size,
                generation_time=generation_time,
                expires_at=expires_at
            )
            
        except Exception as e:
            logger.error(f"PDF报告生成失败: {str(e)}")
            raise
    
    def _add_basic_info(self, analysis_result: VirtualPatientAnalysisResult, styles) -> list:
        """添加基本信息"""
        story = []
        
        # 基本信息表格
        basic_info_data = [
            ['会话ID', analysis_result.session_id],
            ['分析时间', analysis_result.analysis_timestamp.strftime('%Y-%m-%d %H:%M:%S')],
            ['总体得分', f"{analysis_result.overall_score:.1f}分"],
            ['等级评定', analysis_result.grade],
            ['分析耗时', f"{analysis_result.analysis_duration:.2f}秒"]
        ]
        
        basic_info_table = Table(basic_info_data, colWidths=[2*inch, 3*inch])
        basic_info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (1, 0), (1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(Paragraph("基本信息", styles['Heading2']))
        story.append(Spacer(1, 12))
        story.append(basic_info_table)
        story.append(Spacer(1, 20))
        
        return story
    
    def _add_analysis_results(self, analysis_result: VirtualPatientAnalysisResult, styles, include_charts: bool) -> list:
        """添加分析结果"""
        story = []
        
        story.append(Paragraph("分析结果", styles['Heading2']))
        story.append(Spacer(1, 12))
        
        # 1. 情绪价值分析
        story.append(Paragraph("1. 情绪价值分析", styles['Heading3']))
        story.append(Paragraph(f"得分: {analysis_result.emotional_value_score:.1f}分", styles['Normal']))
        
        emotional_details = analysis_result.emotional_value.get('detailed_feedback', '暂无详细反馈')
        story.append(Paragraph(f"详细分析: {emotional_details}", styles['Normal']))
        story.append(Spacer(1, 12))
        
        # 2. 诊断正确性分析
        story.append(Paragraph("2. 诊断正确性分析", styles['Heading3']))
        story.append(Paragraph(f"得分: {analysis_result.diagnosis_accuracy_score:.1f}分", styles['Normal']))
        
        diagnosis_details = analysis_result.diagnosis_accuracy.get('detailed_feedback', '暂无详细反馈')
        story.append(Paragraph(f"详细分析: {diagnosis_details}", styles['Normal']))
        story.append(Spacer(1, 12))
        
        # 3. 科室分配正确性分析
        story.append(Paragraph("3. 科室分配正确性分析", styles['Heading3']))
        story.append(Paragraph(f"得分: {analysis_result.department_accuracy_score:.1f}分", styles['Normal']))
        
        department_details = analysis_result.department_accuracy.get('detailed_feedback', '暂无详细反馈')
        story.append(Paragraph(f"详细分析: {department_details}", styles['Normal']))
        story.append(Spacer(1, 12))
        
        # 添加得分图表
        if include_charts:
            story.extend(self._add_score_chart(analysis_result))
        
        return story
    
    def _add_conversation_display(self, analysis_result: VirtualPatientAnalysisResult, styles) -> list:
        """添加对话记录展示"""
        story = []
        
        story.append(Paragraph("4. 对话记录展示", styles['Heading2']))
        story.append(Spacer(1, 12))
        
        # 对话摘要
        summary = analysis_result.conversation_summary or "暂无对话摘要"
        story.append(Paragraph(f"对话摘要: {summary}", styles['Normal']))
        story.append(Spacer(1, 12))
        
        # 对话统计
        metadata = analysis_result.conversation_metadata
        stats_data = [
            ['总轮数', str(metadata.get('total_rounds', 0))],
            ['对话时长', f"{metadata.get('conversation_duration', 0):.1f}分钟"],
            ['消息总数', str(metadata.get('message_count', 0))]
        ]
        
        stats_table = Table(stats_data, colWidths=[2*inch, 2*inch])
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(Paragraph("对话统计", styles['Heading3']))
        story.append(stats_table)
        story.append(Spacer(1, 20))
        
        return story
    
    def _add_recommendations(self, analysis_result: VirtualPatientAnalysisResult, styles) -> list:
        """添加建议和总结"""
        story = []
        
        story.append(Paragraph("5. 总体建议", styles['Heading2']))
        story.append(Spacer(1, 12))
        
        # 添加建议列表
        for i, recommendation in enumerate(analysis_result.overall_recommendations, 1):
            story.append(Paragraph(f"{i}. {recommendation}", styles['Normal']))
            story.append(Spacer(1, 6))
        
        story.append(Spacer(1, 20))
        
        # 添加总结
        story.append(Paragraph("总结", styles['Heading3']))
        summary_text = f"本次虚拟患者对话分析总体得分为{analysis_result.overall_score:.1f}分，等级评定为{analysis_result.grade}。"
        story.append(Paragraph(summary_text, styles['Normal']))
        
        return story
    
    def _add_score_chart(self, analysis_result: VirtualPatientAnalysisResult) -> list:
        """添加得分图表"""
        story = []
        
        try:
            # 创建柱状图
            drawing = Drawing(400, 200)
            chart = VerticalBarChart()
            chart.x = 50
            chart.y = 50
            chart.height = 125
            chart.width = 300
            
            chart.data = [[
                analysis_result.emotional_value_score,
                analysis_result.diagnosis_accuracy_score,
                analysis_result.department_accuracy_score,
                analysis_result.overall_score
            ]]
            
            chart.categoryAxis.categoryNames = ['情绪价值', '诊断正确性', '科室分配', '总体得分']
            chart.valueAxis.valueMin = 0
            chart.valueAxis.valueMax = 100
            
            chart.bars[0].fillColor = colors.lightblue
            
            drawing.add(chart)
            story.append(drawing)
            story.append(Spacer(1, 20))
            
        except Exception as e:
            logger.warning(f"图表生成失败: {str(e)}")
        
        return story
    
    async def _save_pdf_info(self, session_id: str, filename: str, file_path: str, expires_at: datetime):
        """保存PDF信息到数据库"""
        try:
            db_service = await get_database()
            db = db_service.database
            
            pdf_info = {
                "session_id": session_id,
                "filename": filename,
                "file_path": file_path,
                "created_at": datetime.now(),
                "expires_at": expires_at,
                "download_count": 0
            }
            
            await db.pdf_files.insert_one(pdf_info)
            logger.info(f"PDF信息保存成功: {filename}")
            
        except Exception as e:
            logger.error(f"保存PDF信息失败: {str(e)}")
    
    async def get_pdf_file(self, session_id: str) -> Optional[str]:
        """获取PDF文件路径"""
        try:
            db_service = await get_database()
            db = db_service.database
            
            pdf_info = await db.pdf_files.find_one({"session_id": session_id})
            
            if pdf_info and os.path.exists(pdf_info["file_path"]):
                # 检查是否过期
                if datetime.now() < pdf_info["expires_at"]:
                    # 更新下载次数
                    await db.pdf_files.update_one(
                        {"session_id": session_id},
                        {"$inc": {"download_count": 1}}
                    )
                    return pdf_info["file_path"]
                else:
                    # 删除过期文件
                    await self._cleanup_expired_pdf(session_id, pdf_info["file_path"])
            
            return None
            
        except Exception as e:
            logger.error(f"获取PDF文件失败: {str(e)}")
            return None
    
    async def _cleanup_expired_pdf(self, session_id: str, file_path: str):
        """清理过期PDF文件"""
        try:
            # 删除文件
            if os.path.exists(file_path):
                os.remove(file_path)
            
            # 删除数据库记录
            db_service = await get_database()
            db = db_service.database
            await db.pdf_files.delete_one({"session_id": session_id})
            
            logger.info(f"清理过期PDF文件: {file_path}")
            
        except Exception as e:
            logger.error(f"清理过期PDF文件失败: {str(e)}")

    async def generate_advanced_analysis_pdf(
        self,
        session_id: str,
        analysis_result: Dict[str, Any],
        template_type: str = "advanced"
    ) -> Dict[str, Any]:
        """
        生成高级分析结果PDF报告

        Args:
            session_id: 会话ID
            analysis_result: 高级分析结果数据
            template_type: 模板类型

        Returns:
            Dict: PDF导出响应
        """
        try:
            # 生成PDF文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            pdf_filename = f"advanced_analysis_{session_id}_{timestamp}.pdf"
            pdf_path = os.path.join(self.pdf_storage_path, pdf_filename)

            # 创建PDF文档
            doc = SimpleDocTemplate(
                pdf_path,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )

            story = []
            styles = getSampleStyleSheet()

            # 添加标题
            title_style = ParagraphStyle(
                'AdvancedTitle',
                parent=styles['Heading1'],
                fontSize=24,
                spaceAfter=30,
                alignment=TA_CENTER,
                textColor=colors.darkblue
            )
            story.append(Paragraph("虚拟患者对话分析报告（高级版）", title_style))
            story.append(Spacer(1, 20))

            # 添加执行摘要
            story.extend(self._add_executive_summary(analysis_result, styles))

            # 添加五维度分析
            story.extend(self._add_five_dimensions_analysis(analysis_result, styles))

            # 添加对话记录
            story.extend(self._add_conversation_summary(analysis_result, styles))

            # 添加改进建议
            story.extend(self._add_improvement_suggestions(analysis_result, styles))

            # 生成PDF
            doc.build(story)

            # 生成下载URL
            download_url = f"/api/pdf/download/{pdf_filename}"

            # 记录到数据库
            await self._save_pdf_record(session_id, pdf_filename, pdf_path)

            logger.info(f"高级PDF生成成功: {pdf_path}")

            return {
                "success": True,
                "pdf_filename": pdf_filename,
                "download_url": download_url,
                "file_size": os.path.getsize(pdf_path),
                "generated_at": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"高级PDF生成失败: {str(e)}")
            return {
                "success": False,
                "error_message": str(e)
            }

    def _add_executive_summary(self, analysis_result: Dict[str, Any], styles) -> list:
        """添加执行摘要"""
        story = []

        # 摘要标题
        summary_title = ParagraphStyle(
            'SummaryTitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            textColor=colors.darkgreen
        )
        story.append(Paragraph("执行摘要", summary_title))

        # 总体得分
        overall_score = analysis_result.get('overall_score', 0)
        grade = analysis_result.get('grade', 'N/A')

        summary_data = [
            ['总体得分', f"{overall_score:.1f}分"],
            ['评级', grade],
            ['分析时间', analysis_result.get('analysis_timestamp', 'N/A')],
            ['会话ID', analysis_result.get('session_id', 'N/A')]
        ]

        summary_table = Table(summary_data, colWidths=[2*inch, 3*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (1, 0), (1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(summary_table)
        story.append(Spacer(1, 20))

        # 对话概况
        conversation_summary = analysis_result.get('conversation_summary', '')
        if conversation_summary:
            story.append(Paragraph("对话概况", styles['Heading3']))
            story.append(Paragraph(conversation_summary, styles['Normal']))
            story.append(Spacer(1, 15))

        return story

    def _add_five_dimensions_analysis(self, analysis_result: Dict[str, Any], styles) -> list:
        """添加五维度分析"""
        story = []

        # 维度分析标题
        dimensions_title = ParagraphStyle(
            'DimensionsTitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            textColor=colors.darkred
        )
        story.append(Paragraph("五维度详细分析", dimensions_title))

        dimensions = analysis_result.get('dimensions', {})

        for dim_name, dimension in dimensions.items():
            # 维度标题
            dim_title = f"{dimension.get('name', dim_name)}"
            story.append(Paragraph(dim_title, styles['Heading3']))

            # 得分和权重
            score = dimension.get('score', 0)
            weight = dimension.get('weight', 0)
            weighted_score = score * weight

            score_data = [
                ['得分', f"{score:.1f}分"],
                ['权重', f"{weight*100:.0f}%"],
                ['加权得分', f"{weighted_score:.1f}分"]
            ]

            score_table = Table(score_data, colWidths=[1.5*inch, 2*inch])
            score_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(score_table)
            story.append(Spacer(1, 10))

            # 详细反馈
            feedback = dimension.get('detailed_feedback', '')
            if feedback:
                story.append(Paragraph("详细反馈:", styles['Heading4']))
                story.append(Paragraph(feedback, styles['Normal']))
                story.append(Spacer(1, 10))

            # 子项得分
            sub_scores = dimension.get('sub_scores', {})
            if sub_scores:
                story.append(Paragraph("细项得分:", styles['Heading4']))
                for sub_name, sub_score in sub_scores.items():
                    sub_text = f"• {sub_name}: {sub_score:.1f}分"
                    story.append(Paragraph(sub_text, styles['Normal']))
                story.append(Spacer(1, 10))

            # 改进建议
            recommendations = dimension.get('recommendations', [])
            if recommendations:
                story.append(Paragraph("改进建议:", styles['Heading4']))
                for rec in recommendations:
                    rec_text = f"• {rec}"
                    story.append(Paragraph(rec_text, styles['Normal']))

            story.append(Spacer(1, 20))

        return story

# 创建全局服务实例
pdf_service = PDFService()
