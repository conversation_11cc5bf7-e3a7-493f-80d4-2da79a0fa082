from fastapi import APIRouter, HTTPException
from typing import Dict
import platform
import psutil
import time

# 创建路由器
router = APIRouter(tags=["健康检查"])

# 健康检查路由
@router.get("/health", summary="健康检查接口")
async def health_check() -> Dict:
    """返回API服务的健康状态和基本系统信息"""
    try:
        # 获取系统信息
        system_info = {
            "system": platform.system(),
            "python_version": platform.python_version(),
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "timestamp": time.time()
        }
        
        return {
            "status": "healthy",
            "message": "API服务正常运行",
            "system_info": system_info
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")