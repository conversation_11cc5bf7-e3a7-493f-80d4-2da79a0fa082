from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
from datetime import datetime
from .conversation import ConversationSession

# 情绪分析请求模型
class AnalysisRequest(BaseModel):
    """情绪分析请求模型"""
    content: str = Field(..., description="用户输入的文本内容")
    user_id: Optional[str] = Field(None, description="用户ID，可选")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外的元数据信息")

# 对话记录项模型
class DialogueItem(BaseModel):
    """对话记录项模型"""
    speaker: str = Field(..., description="发言者（用户/系统）")
    content: str = Field(..., description="发言内容")
    timestamp: Optional[str] = Field(None, description="发言时间戳")

# 建议项模型
class RecommendationItem(BaseModel):
    """建议项模型"""
    title: str = Field(..., description="建议标题")
    content: str = Field(..., description="建议内容")
    category: Optional[str] = Field(None, description="建议类别")

# 抑郁风险评估模型
class DepressionRisk(BaseModel):
    """抑郁风险评估模型"""
    level: str = Field(..., description="风险等级（low/medium/high）")
    text: str = Field(..., description="风险等级文本描述")
    confidence: int = Field(..., description="评估可信度百分比")
    description: str = Field(..., description="详细描述")

# 情绪值模型
class EmotionValues(BaseModel):
    """情绪值模型"""
    positive: int = Field(..., description="积极情绪百分比")
    negative: int = Field(..., description="消极情绪百分比")
    neutral: int = Field(..., description="中性情绪百分比")

# 分析结果模型
class AnalysisResult(BaseModel):
    """分析结果模型"""
    id: str = Field(..., description="分析结果唯一ID")
    timestamp: str = Field(..., description="分析时间戳")
    content: str = Field(..., description="用户输入的原始内容")
    emotion_values: EmotionValues = Field(..., description="情绪值分布")
    emotion_summary: str = Field(..., description="情绪分析摘要")
    depression_risk: DepressionRisk = Field(..., description="抑郁风险评估")
    dialogue_record: List[Dict[str, str]] = Field(..., description="对话记录")
    recommendations: List[Dict[str, str]] = Field(..., description="建议列表")
    scores: Dict[str, int] = Field(..., description="各项评分")

# 分析响应模型
class AnalysisResponse(BaseModel):
    """分析响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="响应消息")
    result: Optional[AnalysisResult] = Field(None, description="分析结果")



# 虚拟患者对话分析请求模型
class VirtualPatientAnalysisRequest(BaseModel):
    """虚拟患者对话分析请求模型"""
    session_id: str = Field(..., description="会话ID")
    conversation_data: Dict[str, Any] = Field(..., description="对话数据")
    patient_info: Dict[str, Any] = Field(..., description="患者信息")

# 虚拟患者对话分析结果模型 - 符合用户需求的5个部分
class VirtualPatientAnalysisResult(BaseModel):
    """虚拟患者对话分析结果模型"""
    session_id: str = Field(..., description="会话ID")

    # 1. 给予的情绪价值
    emotional_value: Dict[str, Any] = Field(..., description="给予的情绪价值分析")
    emotional_value_score: float = Field(..., ge=0.0, le=100.0, description="情绪价值得分")

    # 2. 医生诊断的正确性
    diagnosis_accuracy: Dict[str, Any] = Field(..., description="医生诊断正确性分析")
    diagnosis_accuracy_score: float = Field(..., ge=0.0, le=100.0, description="诊断正确性得分")

    # 3. 分配科室的正确性
    department_accuracy: Dict[str, Any] = Field(..., description="科室分配正确性分析")
    department_accuracy_score: float = Field(..., ge=0.0, le=100.0, description="科室分配正确性得分")

    # 4. 聊天记录展示
    conversation_display: Dict[str, Any] = Field(..., description="聊天记录展示")
    conversation_summary: str = Field(..., description="对话摘要")
    key_interactions: List[Dict[str, Any]] = Field(default_factory=list, description="关键交互")

    # 5. 总体建议和打分
    overall_recommendations: List[str] = Field(default_factory=list, description="总体建议")
    overall_score: float = Field(..., ge=0.0, le=100.0, description="总体得分")
    grade: str = Field(..., description="等级评定")

    # 详细分析数据
    detailed_analysis: Dict[str, Any] = Field(default_factory=dict, description="详细分析数据")

    # 元数据
    analysis_timestamp: datetime = Field(default_factory=datetime.now, description="分析时间")
    analysis_duration: Optional[float] = Field(None, description="分析耗时(秒)")
    patient_info: Dict[str, Any] = Field(..., description="患者信息")
    conversation_metadata: Dict[str, Any] = Field(..., description="对话元数据")

# 虚拟患者对话分析响应模型
class VirtualPatientAnalysisResponse(BaseModel):
    """虚拟患者对话分析响应模型"""
    success: bool = Field(..., description="分析是否成功")
    message: str = Field(..., description="响应消息")
    result: Optional[VirtualPatientAnalysisResult] = Field(None, description="分析结果")

# PDF导出请求模型
class PDFExportRequest(BaseModel):
    """PDF导出请求模型"""
    session_id: str = Field(..., description="会话ID")
    analysis_result: VirtualPatientAnalysisResult = Field(..., description="分析结果")
    template_type: str = Field(default="standard", description="模板类型")
    include_charts: bool = Field(default=True, description="是否包含图表")

# PDF导出响应模型
class PDFExportResponse(BaseModel):
    """PDF导出响应模型"""
    pdf_url: str = Field(..., description="PDF文件URL")
    file_size: int = Field(..., description="文件大小(字节)")
    generation_time: float = Field(..., description="生成时间(秒)")
    expires_at: datetime = Field(..., description="过期时间")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
class PDFExportRequest(BaseModel):
    """PDF导出请求模型"""
    session_id: str = Field(..., description="会话ID")
    analysis_result: VirtualPatientAnalysisResult = Field(..., description="分析结果")
    include_conversation: bool = Field(default=True, description="是否包含对话记录")
    include_charts: bool = Field(default=True, description="是否包含图表")

# PDF导出响应模型
class PDFExportResponse(BaseModel):
    """PDF导出响应模型"""
    success: bool = Field(..., description="导出是否成功")
    message: str = Field(..., description="响应消息")
    pdf_url: Optional[str] = Field(None, description="PDF文件URL")
    file_size: Optional[int] = Field(None, description="文件大小(字节)")
    export_timestamp: datetime = Field(default_factory=datetime.now, description="导出时间")