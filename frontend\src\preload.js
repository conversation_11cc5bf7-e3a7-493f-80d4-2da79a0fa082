// preload.js - 预加载脚本
// 这个脚本在渲染进程中运行，但可以访问Node.js API

const { contextBridge, ipcRenderer } = require('electron');

// 通过contextBridge暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 如果需要与主进程通信，可以在这里添加方法
  // 例如：
  // sendMessage: (message) => ipcRenderer.invoke('send-message', message),
  // onMessage: (callback) => ipcRenderer.on('message', callback)
});

// 确保在渲染进程中不会出现require错误
window.process = {
  env: {
    NODE_ENV: process.env.NODE_ENV
  }
};
