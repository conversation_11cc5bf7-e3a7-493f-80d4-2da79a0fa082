"""
AI服务模块 - 预留AI模型接口

这个模块为各种AI功能预留接口，包括：
1. 数字人生成
2. 对话AI（患者回复生成）
3. 对话分析AI
4. 诊断评估AI

实际部署时需要将这些接口连接到具体的AI模型服务
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from ..models.virtual_patient import VirtualPatientPrompt
from ..models.conversation import ConversationMessage
from .ai_model_manager import ai_model_manager

logger = logging.getLogger(__name__)

class AIService:
    """AI服务类 - 统一管理所有AI模型接口"""
    
    def __init__(self):
        # AI模型服务配置 - 实际部署时需要配置真实的服务地址
        self.avatar_generation_url = "http://localhost:8001/generate-avatar"
        self.conversation_ai_url = "http://localhost:8002/patient-response"
        self.analysis_ai_url = "http://localhost:8003/analyze-conversation"
        self.diagnosis_ai_url = "http://localhost:8004/evaluate-diagnosis"
        
        # 超时配置
        self.timeout = 30.0
        
    async def generate_avatar(self, patient_prompt: VirtualPatientPrompt) -> Optional[str]:
        """
        生成数字人头像

        Args:
            patient_prompt: 患者prompt信息

        Returns:
            str: 数字人头像URL，如果生成失败返回None
        """
        try:
            # 构建患者信息
            patient_info = {
                "name": patient_prompt.name,
                "age": patient_prompt.age,
                "gender": patient_prompt.gender.value,
                "description": patient_prompt.avatar_description or "",
                "personality": patient_prompt.personality_traits
            }

            # 调用AI模型管理器
            result = await ai_model_manager.generate_avatar(patient_info)

            if result and "avatar_url" in result:
                logger.info(f"数字人生成成功: {result.get('image_id', 'unknown')}")
                return result["avatar_url"]
            else:
                logger.warning("数字人生成失败，使用默认头像")
                return f"/static/avatars/default_{patient_prompt.gender.value}.png"

        except Exception as e:
            logger.error(f"数字人生成异常: {str(e)}")
            # 返回默认头像URL
            return f"/static/avatars/default_{patient_prompt.gender.value}.png"
    
    async def get_voice_config(self, patient_prompt: VirtualPatientPrompt) -> Dict[str, Any]:
        """
        获取语音配置
        
        Args:
            patient_prompt: 患者prompt信息
            
        Returns:
            Dict: 语音配置信息
        """
        # 根据患者信息配置语音参数
        voice_config = {
            "voice_id": self._select_voice_id(patient_prompt),
            "speed": 1.0,
            "pitch": 1.0,
            "emotion": patient_prompt.emotional_state,
            "language": "zh-CN"
        }
        
        return voice_config
    
    def _select_voice_id(self, patient_prompt: VirtualPatientPrompt) -> str:
        """根据患者信息选择合适的语音ID"""
        if patient_prompt.gender.value == "male":
            if patient_prompt.age < 30:
                return "young_male_voice"
            elif patient_prompt.age < 60:
                return "middle_aged_male_voice"
            else:
                return "elderly_male_voice"
        else:
            if patient_prompt.age < 30:
                return "young_female_voice"
            elif patient_prompt.age < 60:
                return "middle_aged_female_voice"
            else:
                return "elderly_female_voice"
    
    async def get_patient_response(
        self,
        session_id: str,
        doctor_message: str,
        conversation_history: List[ConversationMessage],
        patient_prompt: str = None
    ) -> str:
        """
        获取AI患者回复

        Args:
            session_id: 会话ID
            doctor_message: 医生消息
            conversation_history: 对话历史
            patient_prompt: 患者prompt信息

        Returns:
            str: 患者回复内容
        """
        try:
            # 构建对话上下文
            context = self._build_conversation_context(conversation_history)

            # 获取患者prompt（如果没有提供）
            if not patient_prompt:
                patient_prompt = await self._get_patient_prompt(session_id)

            # 调用AI模型管理器
            result = await ai_model_manager.get_patient_response(
                session_id=session_id,
                doctor_message=doctor_message,
                conversation_context=context,
                patient_prompt=patient_prompt
            )

            if result and "patient_response" in result:
                patient_response = result["patient_response"]

                # 记录token使用情况
                tokens_used = result.get("tokens_used", 0)
                if tokens_used > 0:
                    logger.info(f"对话AI调用成功，使用tokens: {tokens_used}")

                return patient_response or "我不太明白您的意思，能再说一遍吗？"
            else:
                logger.error("对话AI返回无效结果")
                return self._get_fallback_patient_response(doctor_message)

        except Exception as e:
            logger.error(f"患者回复生成异常: {str(e)}")
            return self._get_fallback_patient_response(doctor_message)

    async def _get_patient_prompt(self, session_id: str) -> str:
        """获取患者prompt信息"""
        try:
            # 这里应该从数据库或缓存中获取患者prompt
            # 暂时返回默认prompt
            return """
你是一位30岁的男性患者，因为胸痛来医院就诊。
你感到焦虑和担心，但愿意配合医生的检查。
请根据这个设定回答医生的问题。
"""
        except Exception as e:
            logger.error(f"获取患者prompt失败: {str(e)}")
            return "你是一位患者，请配合医生的问诊。"

    def _get_fallback_patient_response(self, doctor_message: str) -> str:
        """获取备用患者回复"""
        # 基于医生消息类型返回合适的备用回复
        if "你好" in doctor_message or "您好" in doctor_message:
            return "医生您好，我感觉不太舒服。"
        elif "症状" in doctor_message or "不舒服" in doctor_message:
            return "我主要是感觉胸部有些疼痛。"
        elif "多长时间" in doctor_message or "什么时候" in doctor_message:
            return "大概有几个小时了。"
        elif "检查" in doctor_message:
            return "好的，我配合检查。"
        else:
            return "我现在有点不舒服，请您耐心一点。"
    
    def _build_conversation_context(self, conversation_history: List[ConversationMessage]) -> List[Dict[str, str]]:
        """构建对话上下文"""
        context = []
        for message in conversation_history[-10:]:  # 只取最近10条消息
            context.append({
                "role": message.message_type.value,
                "content": message.content,
                "timestamp": message.timestamp.isoformat()
            })
        return context
    
    async def get_conversation_suggestions(
        self,
        session_id: str,
        conversation_history: List[ConversationMessage]
    ) -> Optional[List[str]]:
        """
        获取对话建议
        
        Args:
            session_id: 会话ID
            conversation_history: 对话历史
            
        Returns:
            List[str]: 建议的下一步问题或行动
        """
        try:
            # 分析当前对话状态，提供建议
            suggestions = [
                "询问患者的主要症状",
                "了解症状的持续时间",
                "询问是否有相关的既往病史",
                "进行必要的体格检查",
                "考虑进一步的检查项目"
            ]
            
            # 这里可以调用AI模型来生成更智能的建议
            return suggestions[:3]  # 返回前3个建议
            
        except Exception as e:
            logger.error(f"获取对话建议异常: {str(e)}")
            return None
    
    async def analyze_conversation_for_ai(
        self,
        session_id: str,
        conversation_data: Dict[str, Any],
        patient_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        AI对话分析
        
        Args:
            session_id: 会话ID
            conversation_data: 对话数据
            patient_info: 患者信息
            
        Returns:
            Dict: AI分析结果
            
        模型部署说明：
        1. 部署对话分析AI模型到指定端口（如8003）
        2. 模型分析医生的沟通技巧、共情能力等
        3. 返回详细的分析报告
        
        推荐模型：
        - 专门训练的医疗对话分析模型
        - 基于BERT/RoBERTa的情感分析模型
        - 多模态分析模型（结合语音语调）
        """
        try:
            # 调用AI模型管理器
            result = await ai_model_manager.analyze_conversation(
                session_id=session_id,
                conversation_data=conversation_data,
                patient_info=patient_info
            )

            if result:
                logger.info("AI对话分析完成")
                return result
            else:
                logger.error("AI对话分析返回空结果")
                return self._get_default_analysis_result()

        except Exception as e:
            logger.error(f"AI对话分析异常: {str(e)}")
            return self._get_default_analysis_result()
    
    async def evaluate_diagnosis(
        self,
        doctor_diagnosis: str,
        correct_diagnosis: str,
        conversation_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        AI诊断评估
        
        Args:
            doctor_diagnosis: 医生诊断
            correct_diagnosis: 正确诊断
            conversation_data: 对话数据
            
        Returns:
            Dict: 诊断评估结果
            
        模型部署说明：
        1. 部署诊断评估AI模型到指定端口（如8004）
        2. 模型比较医生诊断与标准答案的相似度
        3. 分析诊断过程的合理性
        
        推荐模型：
        - 医疗知识图谱 + 语义相似度模型
        - 专门的医疗诊断评估模型
        - 基于医疗文献训练的大语言模型
        """
        try:
            # 调用AI模型管理器
            result = await ai_model_manager.evaluate_diagnosis(
                doctor_diagnosis=doctor_diagnosis,
                correct_diagnosis=correct_diagnosis,
                conversation_data=conversation_data
            )

            if result:
                logger.info("AI诊断评估完成")
                return result
            else:
                logger.error("AI诊断评估返回空结果")
                return self._get_default_diagnosis_evaluation()

        except Exception as e:
            logger.error(f"AI诊断评估异常: {str(e)}")
            return self._get_default_diagnosis_evaluation()
    
    def _get_default_analysis_result(self) -> Dict[str, Any]:
        """获取默认分析结果"""
        return {
            "emotional_value_score": 75.0,
            "communication_quality": 80.0,
            "empathy_level": 70.0,
            "information_gathering": 85.0,
            "analysis_timestamp": datetime.now().isoformat()
        }
    
    def _get_default_diagnosis_evaluation(self) -> Dict[str, Any]:
        """获取默认诊断评估结果"""
        return {
            "accuracy_score": 80.0,
            "reasoning_quality": 75.0,
            "completeness": 70.0,
            "evaluation_timestamp": datetime.now().isoformat()
        }

# 创建全局AI服务实例
ai_service = AIService()
