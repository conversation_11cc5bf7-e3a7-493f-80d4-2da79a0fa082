# 语音模型部署指南

本文档详细说明如何部署和配置虚拟患者对话系统中的语音处理模型，包括STT（语音转文字）、TTS（文字转语音）和Whisper语音识别。

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端 Vue.js 应用                         │
│  ┌─────────────────┐  ┌─────────────────────────────────┐   │
│  │   语音录制组件   │  │        音频播放组件              │   │
│  │   VoiceRecorder │  │        AudioPlayer              │   │
│  └─────────────────┘  └─────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                    后端 FastAPI 服务                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  音频服务接口                            │ │
│  │  /api/conversation/audio/stt                           │ │
│  │  /api/conversation/audio/tts                           │ │
│  │  /api/conversation/audio/whisper                       │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    语音模型服务层                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   STT 服务      │  │   TTS 服务      │  │ Whisper服务 │ │
│  │   端口: 8005    │  │   端口: 8006    │  │ 端口: 8007  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 1. STT (Speech-to-Text) 服务部署

### 1.1 推荐模型

#### 选项1: OpenAI Whisper (推荐)
- **优势**: 高准确率，支持多语言，开源免费
- **部署方式**: 本地部署或API调用
- **资源需求**: GPU推荐，CPU也可运行

#### 选项2: Google Speech-to-Text
- **优势**: 云服务，稳定可靠
- **部署方式**: API调用
- **费用**: 按使用量计费

#### 选项3: 百度语音识别
- **优势**: 中文识别效果好
- **部署方式**: API调用
- **费用**: 有免费额度

### 1.2 Whisper本地部署

```bash
# 1. 安装依赖
pip install openai-whisper
pip install torch torchvision torchaudio

# 2. 下载模型
whisper --model medium --download-root ./models

# 3. 创建服务脚本
```

**whisper_service.py**:
```python
from fastapi import FastAPI, File, UploadFile, Form
import whisper
import tempfile
import os

app = FastAPI()

# 加载模型
model = whisper.load_model("medium")

@app.post("/transcribe")
async def transcribe_audio(
    audio: UploadFile = File(...),
    language: str = Form("zh")
):
    # 保存临时文件
    with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
        content = await audio.read()
        tmp_file.write(content)
        tmp_file_path = tmp_file.name
    
    try:
        # 转录音频
        result = model.transcribe(tmp_file_path, language=language)
        
        return {
            "text": result["text"],
            "confidence": 0.95,  # Whisper不直接提供置信度
            "language": result["language"],
            "duration": result.get("duration", 0)
        }
    finally:
        # 清理临时文件
        os.unlink(tmp_file_path)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8007)
```

### 1.3 Docker部署

**Dockerfile**:
```dockerfile
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制代码
COPY whisper_service.py .

# 下载模型
RUN python -c "import whisper; whisper.load_model('medium')"

EXPOSE 8007

CMD ["python", "whisper_service.py"]
```

## 2. TTS (Text-to-Speech) 服务部署

### 2.1 推荐模型

#### 选项1: Azure Speech Services (推荐)
- **优势**: 高质量语音，支持多种语音风格
- **部署方式**: API调用
- **费用**: 按字符计费

#### 选项2: Google Text-to-Speech
- **优势**: 自然语音，支持SSML
- **部署方式**: API调用
- **费用**: 按字符计费

#### 选项3: VITS (开源方案)
- **优势**: 开源免费，可本地部署
- **部署方式**: 本地部署
- **资源需求**: GPU推荐

### 2.2 Azure TTS部署示例

**azure_tts_service.py**:
```python
from fastapi import FastAPI
from pydantic import BaseModel
import azure.cognitiveservices.speech as speechsdk
import tempfile
import os

app = FastAPI()

# Azure配置
SPEECH_KEY = "your_speech_key"
SPEECH_REGION = "your_region"

class TTSRequest(BaseModel):
    text: str
    voice_id: str = "zh-CN-XiaoxiaoNeural"
    speed: float = 1.0
    pitch: float = 1.0

@app.post("/synthesize")
async def synthesize_speech(request: TTSRequest):
    # 配置语音服务
    speech_config = speechsdk.SpeechConfig(
        subscription=SPEECH_KEY, 
        region=SPEECH_REGION
    )
    speech_config.speech_synthesis_voice_name = request.voice_id
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
        tmp_file_path = tmp_file.name
    
    # 配置音频输出
    audio_config = speechsdk.audio.AudioOutputConfig(filename=tmp_file_path)
    
    # 创建合成器
    synthesizer = speechsdk.SpeechSynthesizer(
        speech_config=speech_config, 
        audio_config=audio_config
    )
    
    # 构建SSML
    ssml = f"""
    <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-CN">
        <voice name="{request.voice_id}">
            <prosody rate="{request.speed}" pitch="{request.pitch:+.0%}">
                {request.text}
            </prosody>
        </voice>
    </speak>
    """
    
    # 合成语音
    result = synthesizer.speak_ssml_async(ssml).get()
    
    if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
        # 返回音频文件URL
        audio_url = f"/static/audio/{os.path.basename(tmp_file_path)}"
        return {
            "audio_url": audio_url,
            "duration": 0,  # 需要计算实际时长
            "format": "wav"
        }
    else:
        raise Exception(f"语音合成失败: {result.reason}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8006)
```

## 3. 语音情感分析服务

### 3.1 OpenSMILE + 机器学习

**emotion_analysis_service.py**:
```python
from fastapi import FastAPI, File, UploadFile
import librosa
import numpy as np
import joblib
import tempfile
import os

app = FastAPI()

# 加载预训练的情感分析模型
emotion_model = joblib.load("emotion_model.pkl")

@app.post("/analyze_emotion")
async def analyze_audio_emotion(audio: UploadFile = File(...)):
    # 保存临时文件
    with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
        content = await audio.read()
        tmp_file.write(content)
        tmp_file_path = tmp_file.name
    
    try:
        # 提取音频特征
        y, sr = librosa.load(tmp_file_path, sr=16000)
        
        # 提取MFCC特征
        mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
        mfcc_mean = np.mean(mfccs, axis=1)
        
        # 提取其他特征
        spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)
        spectral_rolloff = librosa.feature.spectral_rolloff(y=y, sr=sr)
        zero_crossing_rate = librosa.feature.zero_crossing_rate(y)
        
        # 组合特征
        features = np.concatenate([
            mfcc_mean,
            np.mean(spectral_centroids),
            np.mean(spectral_rolloff),
            np.mean(zero_crossing_rate)
        ]).reshape(1, -1)
        
        # 预测情感
        emotion_probs = emotion_model.predict_proba(features)[0]
        emotion_labels = ["neutral", "happy", "sad", "angry", "fear"]
        
        # 找到最可能的情感
        max_idx = np.argmax(emotion_probs)
        predicted_emotion = emotion_labels[max_idx]
        confidence = emotion_probs[max_idx]
        
        return {
            "emotion": predicted_emotion,
            "confidence": float(confidence),
            "emotions": {
                label: float(prob) 
                for label, prob in zip(emotion_labels, emotion_probs)
            }
        }
    finally:
        os.unlink(tmp_file_path)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8008)
```

## 4. 部署配置

### 4.1 Docker Compose部署

**docker-compose.yml**:
```yaml
version: '3.8'

services:
  whisper-service:
    build: ./whisper
    ports:
      - "8007:8007"
    volumes:
      - ./models:/app/models
    environment:
      - MODEL_SIZE=medium
  
  tts-service:
    build: ./tts
    ports:
      - "8006:8006"
    environment:
      - AZURE_SPEECH_KEY=${AZURE_SPEECH_KEY}
      - AZURE_SPEECH_REGION=${AZURE_SPEECH_REGION}
  
  emotion-service:
    build: ./emotion
    ports:
      - "8008:8008"
    volumes:
      - ./emotion_models:/app/models

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./static:/usr/share/nginx/html/static
```

### 4.2 Nginx配置

**nginx.conf**:
```nginx
events {
    worker_connections 1024;
}

http {
    upstream whisper_backend {
        server whisper-service:8007;
    }
    
    upstream tts_backend {
        server tts-service:8006;
    }
    
    upstream emotion_backend {
        server emotion-service:8008;
    }
    
    server {
        listen 80;
        
        location /whisper/ {
            proxy_pass http://whisper_backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /tts/ {
            proxy_pass http://tts_backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /emotion/ {
            proxy_pass http://emotion_backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /static/ {
            root /usr/share/nginx/html;
            expires 1d;
        }
    }
}
```

## 5. 性能优化建议

### 5.1 模型优化
- 使用量化模型减少内存占用
- 实现模型缓存机制
- 批处理请求提高吞吐量

### 5.2 音频处理优化
- 音频格式标准化（16kHz, 16bit, mono）
- 实现音频压缩和流式传输
- 添加音频质量检测

### 5.3 缓存策略
- Redis缓存常用TTS结果
- 本地缓存音频文件
- CDN加速音频文件分发

## 6. 监控和日志

### 6.1 性能监控
- 响应时间监控
- 错误率统计
- 资源使用监控

### 6.2 日志配置
```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('voice_service.log'),
        logging.StreamHandler()
    ]
)
```

## 7. 安全考虑

### 7.1 API安全
- 实现API密钥认证
- 请求频率限制
- 输入数据验证

### 7.2 数据安全
- 音频数据加密传输
- 临时文件自动清理
- 用户隐私保护

## 8. 故障排除

### 8.1 常见问题
1. **音频格式不支持**: 确保音频格式为WAV/MP3
2. **模型加载失败**: 检查模型文件路径和权限
3. **API调用超时**: 调整超时设置和重试机制

### 8.2 调试工具
- 音频质量检测工具
- API响应时间分析
- 错误日志分析

通过以上配置，可以构建一个完整的语音处理服务体系，为虚拟患者对话系统提供高质量的语音交互功能。
